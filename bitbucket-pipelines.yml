image: sleavely/node-awscli

pipelines:
  branches:
    staging:
      - step:
          name: Deploy to Staging
          deployment: staging
          script:
            - aws --version
            - export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
            - export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
            - export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
            - bash deploy-staging.sh
    master:
      - step:
          name: Deploy to Production
          deployment: production
          script:
            - aws --version
            - export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
            - export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
            - export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
            - bash deploy-live.sh
