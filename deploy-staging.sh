#!/bin/bash

set -e

export CLICOLOR=1
export LSCOLORS=exfxcxdxbxegedabagacad
# Reset
export ColorOff='\e[0m'  # Reset
# Regular Colors
export Black='\e[0;30m'  # Black
export Red='\e[0;31m'    # Red
export Green='\e[0;32m'  # Green
export Yellow='\e[0;33m' # Yellow
export Blue='\e[0;34m'   # Blue
export Purple='\e[0;35m' # Purple
export Cyan='\e[0;36m'   # Cyan
export White='\e[0;37m'  # White

print_message() {
  printf "${Yellow}\n\n>"
  printf "${Yellow}\n> ${Red}$( date +%H:%M )${Yellow} - $1"
  printf "${Yellow}\n>\n\n"
}

  print_message "Deploying the ${Purple}Admin Panel${Yellow}..."

  location=build

  subdomain='admin-staging'
  distribution='E305Z0I2OSCWIF'

  print_message "Installing dependencies......."
  npm install
  print_message "Building for staging......."
  npm run build

  print_message "Successfully built the ${Purple}Admin Panel${Yellow}..."

  aws s3 sync ./$location "s3://$subdomain.repd.us" --region us-east-1 --acl public-read --exclude .DS_Store

  print_message "Successfully uploaded files..."

  aws cloudfront create-invalidation --distribution-id $distribution --paths '/*'

  print_message "Rep’d Admin Panel Deployed To Staging"
