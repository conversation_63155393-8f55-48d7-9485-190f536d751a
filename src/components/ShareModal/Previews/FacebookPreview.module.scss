@import 'styles/variables';

.facebookPreview {
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e4e6ea;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.previewHeader {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-bottom: 1px solid #e4e6ea;
  background-color: #f0f2f5;
}

.previewLogo {
  width: 20px;
  height: 20px;
}

.previewPlatformName {
  font-size: 14px;
  font-weight: 600;
  color: $dark-blue;
}

.previewContent {
  padding: 0;
}

// Post Header
.postHeader {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  gap: 12px;
}

.profilePicture {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1877f2;
  flex-shrink: 0;
}

.postInfo {
  flex: 1;
}

.pageNameContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.pageName {
  font-size: 15px;
  font-weight: 600;
  color: #050505;
}

.followButton {
  font-size: 13px;
  color: #1877f2;
  font-weight: 600;
}

.postMeta {
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeStamp {
  font-size: 13px;
  color: #65676b;
}

.visibility {
  font-size: 12px;
}

.postActions {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #65676b;
}

.moreOptions,
.closeButton {
  font-size: 16px;
  cursor: pointer;
}

// Post Content
.postText {
  padding: 0 16px 12px 16px;
  font-size: 15px;
  line-height: 1.33;
  color: #050505;
  white-space: pre-wrap;
  text-align: left;
}

// Media
.mediaContainer {
  position: relative;
  margin-bottom: 12px;
}

.mediaImage {
  width: 100%;
  height: auto;
  display: block;
}

.playButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

// Engagement Stats
.engagementStats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #e4e6ea;
}

.reactions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.reactionIcons {
  display: flex;
  align-items: center;
  gap: -2px;
}

.reactionIcon {
  font-size: 16px;
  margin-left: -2px;

  &:first-child {
    margin-left: 0;
  }
}

.reactionCount {
  font-size: 15px;
  color: #65676b;
  margin-left: 4px;
}

.commentsShares {
  display: flex;
  gap: 12px;
  font-size: 15px;
  color: #65676b;
}

// Action Buttons
.actionButtons {
  display: flex;
  padding: 4px 0;
}

.actionButton {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px;
  background: none;
  border: none;
  color: #65676b;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  border-radius: 6px;
  margin: 0 4px;

  &:hover {
    background-color: #f2f2f2;
  }
}

.actionIcon {
  font-size: 16px;
}