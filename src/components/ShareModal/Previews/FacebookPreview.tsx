import React from 'react';
import classes from './FacebookPreview.module.scss';
import facebookLogo from 'assets/logo/facebook.png';

interface FacebookPreviewProps {
  content: string;
  videoThumbnail?: string;
}

export default function FacebookPreview({ content, videoThumbnail }: FacebookPreviewProps) {
  return (
    <div className={classes.facebookPreview}>
      <div className={classes.previewHeader}>
        <img
          src={facebookLogo}
          alt="Facebook logo"
          className={classes.previewLogo}
        />
        <span className={classes.previewPlatformName}>
          Facebook Preview
        </span>
      </div>

      <div className={classes.previewContent}>
        {/* Post Header */}
        <div className={classes.postHeader}>
          <div className={classes.profilePicture}></div>
          <div className={classes.postInfo}>
            <div className={classes.pageNameContainer}>
              <span className={classes.pageName}>Your Page Name</span>
              <span className={classes.followButton}>Follow</span>
            </div>
            <div className={classes.postMeta}>
              <span className={classes.timeStamp}>4d</span>
              <span className={classes.visibility}>🌍</span>
            </div>
          </div>
          <div className={classes.postActions}>
            <span className={classes.moreOptions}>•••</span>
            <span className={classes.closeButton}>✕</span>
          </div>
        </div>

        {/* Post Content */}
        {content && (
          <div className={classes.postText}>
            {content}
          </div>
        )}

        {/* Video/Media */}
        {videoThumbnail && (
          <div className={classes.mediaContainer}>
            <img
              src={videoThumbnail}
              alt="Video preview"
              className={classes.mediaImage}
            />
            <div className={classes.playButton}>▶</div>
          </div>
        )}

        {/* Engagement Stats */}
        <div className={classes.engagementStats}>
          <div className={classes.reactions}>
            <div className={classes.reactionIcons}>
              <span className={classes.reactionIcon}>❤️</span>
              <span className={classes.reactionIcon}>👍</span>
              <span className={classes.reactionIcon}>😂</span>
            </div>
            <span className={classes.reactionCount}>868</span>
          </div>
          <div className={classes.commentsShares}>
            <span>64 comments</span>
            <span>68 shares</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={classes.actionButtons}>
          <button className={classes.actionButton}>
            <span className={classes.actionIcon}>👍</span>
            <span>Like</span>
          </button>
          <button className={classes.actionButton}>
            <span className={classes.actionIcon}>💬</span>
            <span>Comment</span>
          </button>
          <button className={classes.actionButton}>
            <span className={classes.actionIcon}>↗️</span>
            <span>Share</span>
          </button>
        </div>
      </div>
    </div>
  );
}
