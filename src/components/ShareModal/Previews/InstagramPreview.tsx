import React from 'react';
import classes from './InstagramPreview.module.scss';
import instagramLogo from 'assets/logo/instagram.png';

interface InstagramPreviewProps {
  content: string;
  videoThumbnail?: string;
}

export default function InstagramPreview({ content, videoThumbnail }: InstagramPreviewProps) {
  return (
    <div className={classes.instagramPreview}>
      <div className={classes.previewHeader}>
        <img
          src={instagramLogo}
          alt="Instagram logo"
          className={classes.previewLogo}
        />
        <span className={classes.previewPlatformName}>
          Instagram Preview
        </span>
      </div>

      <div className={classes.previewContent}>
        {content && (
          <div className={classes.previewText}>
            {content}
          </div>
        )}

        {videoThumbnail && (
          <div className={classes.previewVideoContainer}>
            <img
              src={videoThumbnail}
              alt="Video preview"
              className={classes.previewVideo}
            />
            <div className={classes.playButton}>▶</div>
          </div>
        )}
      </div>
    </div>
  );
}
