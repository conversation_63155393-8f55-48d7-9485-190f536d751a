import React from 'react';
import classes from './LinkedInPreview.module.scss';
import linkedinLogo from 'assets/logo/linkedin.png';

interface LinkedInPreviewProps {
  content: string;
  videoThumbnail?: string;
}

export default function LinkedInPreview({ content, videoThumbnail }: LinkedInPreviewProps) {
  return (
    <div className={classes.linkedinPreview}>
      <div className={classes.previewHeader}>
        <img
          src={linkedinLogo}
          alt="LinkedIn logo"
          className={classes.previewLogo}
        />
        <span className={classes.previewPlatformName}>
          LinkedIn Preview
        </span>
      </div>

      <div className={classes.previewContent}>
        {content && (
          <div className={classes.previewText}>
            {content}
          </div>
        )}

        {videoThumbnail && (
          <div className={classes.previewVideoContainer}>
            <img
              src={videoThumbnail}
              alt="Video preview"
              className={classes.previewVideo}
            />
            <div className={classes.playButton}>▶</div>
          </div>
        )}
      </div>
    </div>
  );
}
