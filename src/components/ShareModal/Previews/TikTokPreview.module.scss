@import 'styles/variables';

.tiktokPreview {
  background-color: white;
  border-radius: 12px;
  border: 1px solid $grey;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.previewHeader {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-bottom: 1px solid $grey;
}

.previewLogo {
  width: 20px;
  height: 20px;
}

.previewPlatformName {
  font-size: 14px;
  font-weight: 600;
  color: $dark-blue;
}

.previewContent {
  padding: 15px;
}

.previewText {
  font-size: 14px;
  line-height: 1.4;
  color: $dark-blue;
  margin-bottom: 15px;
  white-space: pre-wrap;
}

.previewVideoContainer {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.previewVideo {
  width: 100%;
  height: auto;
  display: block;
}

.playButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
