import React from 'react';
import classes from './TikTokPreview.module.scss';
import tiktokLogo from 'assets/logo/tiktok.png';

interface TikTokPreviewProps {
  content: string;
  videoThumbnail?: string;
}

export default function TikTokPreview({ content, videoThumbnail }: TikTokPreviewProps) {
  return (
    <div className={classes.tiktokPreview}>
      <div className={classes.previewHeader}>
        <img
          src={tiktokLogo}
          alt="TikTok logo"
          className={classes.previewLogo}
        />
        <span className={classes.previewPlatformName}>
          TikTok Preview
        </span>
      </div>

      <div className={classes.previewContent}>
        {content && (
          <div className={classes.previewText}>
            {content}
          </div>
        )}

        {videoThumbnail && (
          <div className={classes.previewVideoContainer}>
            <img
              src={videoThumbnail}
              alt="Video preview"
              className={classes.previewVideo}
            />
            <div className={classes.playButton}>▶</div>
          </div>
        )}
      </div>
    </div>
  );
}
