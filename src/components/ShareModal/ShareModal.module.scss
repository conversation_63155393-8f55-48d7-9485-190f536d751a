@import 'styles/variables';

.shareModal {
  width: 100%;
  text-align: center;
  padding: 0;
  position: relative;
  overflow: hidden;

  .closeButton {
    background: none;
    border: none;
    color: $dark-blue;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      color: $blue;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    color: $dark-blue;
  }

  .blueHeader {
    background-color: $blue;
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 0;
    border-radius: 15px 15px 0 0;

    .headerTitle {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      color: white;
    }

    .headerCloseButton {
      position: absolute;
      right: 20px;
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .titleWithClose {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 30px;
    padding: 20px;

    .title {
      margin-bottom: 0;
    }

    .closeButton {
      position: absolute;
      right: 0;
    }
  }

  .connectAccountsContent {
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .videoThumbnailContainer {
      width: 100%;
      margin-bottom: 60px; // Increased margin to account for overflowing thumbnail
      position: relative;
      display: flex;
      justify-content: center;
    }

    .blackDiv {
      background-color: #000;
      width: 100vw;
      max-width: calc(100% + 60px); // Extend beyond any container padding
      height: 240px;
      position: relative;
      overflow: visible; // Allow content to overflow
      margin-left: -30px;
      margin-right: -30px;
    }

    .thumbnailWrapper {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%) translateY(20px); // Center horizontally and push down for overflow
      z-index: 1; // Ensure thumbnail appears above other content
      background-color: rgba(0, 0, 0, 0.1); // Semi-transparent gray background
      border-radius: 12px;
      padding: 20px;
      max-width: 440px; // 400px + 40px padding (20px on each side)
      width: calc(100% - 40px); // Account for container margins
    }

    .videoThumbnail {
      width: 100%;
      max-width: 400px;
      height: calc(240px + 20px - 20px); // Adjust for wrapper padding
      object-fit: cover;
      border-radius: 8px;
      display: block;
    }

    .getStartedTitle {
      font-size: 32px;
      font-weight: 600;
      color: $blue;
      margin: 20px 0 20px 0;
      padding: 0 20px;
    }

    .linkAccountsButton {
      background-color: $blue;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 15px 40px;
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      margin: 0 20px 30px 20px;
      min-width: 300px;

      &:hover {
        background-color: $dark-blue;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .divider {
      width: calc(100% - 40px);
      height: 1px;
      background-color: $grey;
      margin: 0 20px 20px 20px;
    }

    .connectDescription {
      font-size: 16px;
      color: $dark-grey;
      line-height: 1.5;
      margin: 0;
      max-width: 400px;
      padding: 0 20px 20px 20px;
    }

    .stepDescription {
      font-size: 16px;
      color: $dark-grey;
      line-height: 1.5;
      text-align: center;
      margin: 30px 0 30px 0;
      max-width: 400px;
      padding: 0 20px;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid $grey;
      border-top: 4px solid $dark-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0px auto;
      margin-bottom: 40px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  }

  .stepContent {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;

    p {
      margin: 0;
      font-size: 16px;
      color: $dark-grey;
      line-height: 1.5;
    }

    .stepTitle {
      font-size: 20px;
      font-weight: 600;
      color: $dark-blue;
      margin: 0 0 20px 0;
      text-align: center;
    }

    .stepDescription {
      font-size: 16px;
      color: $dark-grey;
      line-height: 1.5;
      text-align: center;
      margin: 0 0 30px 0;
      max-width: 400px;
    }

    .connectButton {
      background-color: $dark-blue;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 15px 30px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 200px;

      &:hover {
        background-color: $blue;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid $grey;
      border-top: 4px solid $dark-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  }

  .shareVideoContent {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 20px;
  }

  .shareContainer {
    display: flex;
    gap: 20px;
    width: 100%;
  }

  .leftColumn {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .rightColumn {
    width: 400px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .rightColumnContent {
    background-color: #f8f9fa;
    border-radius: 8px;
    min-height: 300px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .multiSelect {
    position: relative;
    width: 100%;
  }

  .multiSelectTrigger {
    width: 100%;
    padding: 12px;
    border: 1px solid $grey;
    border-radius: 8px;
    font-size: 16px;
    color: $dark-blue;
    background-color: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;

    &:hover {
      border-color: $blue;
    }

    span {
      flex: 1;
      text-align: left;
    }
  }

  .chevron {
    transition: transform 0.2s ease;
    color: #666;
    width: auto !important;

    &.open {
      transform: rotate(180deg);
    }
  }

  .multiSelectDropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid $grey;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .multiSelectOption {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: $pale-blue;
      color: $dark-blue;
      font-weight: 500;
    }

    span {
      width: auto !important;
      font-size: 16px;
    }
  }

  .checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .textboxContainer {
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .textbox {
    width: 100%;
    min-height: 120px;
    max-height: 300px;
    padding: 12px;
    padding-bottom: 50px; // Make room for the button
    border: 1px solid $grey;
    border-radius: 8px;
    font-size: 16px;
    color: $dark-blue;
    background-color: white;
    resize: none; // Disable manual resize since we'll auto-expand
    font-family: inherit;
    overflow-y: auto;
    line-height: 1.5;

    &:focus {
      outline: none;
      border-color: $blue;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: $dark-grey;
    }

    // Auto-expand based on content
    &:not(:focus) {
      height: auto;
      min-height: 120px;
    }
  }

  .generateButton {
    position: absolute;
    bottom: 8px;
    left: 8px;
    right: 8px;
    background-color: white;
    color: $dark-blue;
    border: 2px solid $grey;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10; // Ensure button stays on top

    &:hover {
      border-color: $blue;
      background-color: $pale-blue;
    }

    &:active {
      transform: translateY(1px);
    }
  }

  .scheduleButton {
    width: 100%;
    height: 45px;
    background-color: white;
    color: $dark-blue;
    border: 2px solid $grey;
    border-radius: 8px;
    padding: 9px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: $blue;
      background-color: $pale-blue;
    }

    &:active {
      transform: translateY(1px);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        border-color: $grey;
        background-color: white;
      }
    }
  }

  .answerName {
    padding: 12px;
    background-color: $pale-blue;
    border-radius: 8px;
    font-size: 14px;
    color: $dark-blue;
    font-weight: 500;
  }

  .platformContainersWrapper {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }

  .platformContainer {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .platformDivider {
    width: 100%;
    height: 1px;
    background-color: #e9ecef;
    margin: 20px 0;
  }

  .platformLogo {
    width: 32px;
    height: 32px;
    object-fit: contain;
    align-self: flex-start;
  }

  .platformPostButton {
    width: 100%;
    background-color: $dark-blue;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: $blue;
    }

    &:active {
      transform: translateY(1px);
    }

    &.successButton {
      background-color: $green;
      cursor: default;

      &:hover {
        background-color: $green;
      }

      &:active {
        transform: none;
      }
    }

    .buttonSpinner {
      margin-right: 8px;
    }
  }

  .shareButton {
    width: 100%;
    background-color: $dark-blue;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px; // Taller than generateButton (8px)
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: $blue;
    }

    &:active {
      transform: translateY(1px);
    }
  }

  .previewsContainer {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .mobilePreview {
    background-color: white;
    border-radius: 12px;
    border: 1px solid $grey;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .mobilePreviewHeader {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-bottom: 1px solid $grey;
    background-color: #f8f9fa;
  }

  .previewLogo {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  .previewPlatformName {
    font-size: 14px;
    font-weight: 600;
    color: $dark-blue;
  }

  .mobilePreviewContent {
    padding: 15px;
  }

  .previewText {
    font-size: 14px;
    line-height: 1.4;
    color: $charcoal;
    margin-bottom: 15px;
    white-space: pre-wrap;
  }

  .previewVideoContainer {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background-color: #000;
  }

  .previewVideo {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
  }

  .playButton {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: $dark-blue;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: white;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  .connectMoreButton {
    width: 100%;
    background-color: white;
    color: $dark-blue;
    border: 2px solid $grey;
    border-radius: 8px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: $blue;
      background-color: $pale-blue;
    }

    &:active {
      transform: translateY(1px);
    }
  }
}

@media (max-width: 640px) {
  .shareModal {
    .blueHeader {
      padding: 15px;

      .headerTitle {
        font-size: 20px;
      }

      .headerCloseButton {
        right: 15px;
      }
    }

    .titleWithClose {
      padding: 15px;
    }

    .connectAccountsContent {
      .getStartedTitle {
        font-size: 24px;
        padding: 0 15px;
      }

      .linkAccountsButton {
        min-width: 250px;
        font-size: 16px;
        margin: 0 15px 30px 15px;
      }

      .blackDiv {
        height: 150px;
        margin-left: -10px;
        margin-right: -10px;
        max-width: calc(100% + 20px);
      }

      .divider {
        width: calc(100% - 30px);
        margin: 0 15px 20px 15px;
      }

      .connectDescription {
        padding: 0 15px 15px 15px;
      }

      .stepDescription {
        padding: 0 15px;
      }
    }

    .stepContent {
      min-height: 150px;
      padding: 15px;
    }

    .shareVideoContent {
      padding: 15px;
    }

    .shareContainer {
      flex-direction: column;
    }

    .rightColumn {
      width: 100%;
      padding: 15px;
    }

    .previewVideo {
      height: 150px;
    }

    .socialMediaSelect {
      min-height: 100px;
    }

    .textbox {
      min-height: 100px;
    }
  }
}