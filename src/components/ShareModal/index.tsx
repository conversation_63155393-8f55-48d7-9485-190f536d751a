import React, { useState, useEffect, useRef } from 'react';
import Modal from 'shared/Modal';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { toast } from 'react-toastify';
import cn from 'classnames';

import classes from './ShareModal.module.scss';

// Import social media logos
import facebookLogo from 'assets/logo/facebook.png';
import instagramLogo from 'assets/logo/instagram.png';
import linkedinLogo from 'assets/logo/linkedin.png';
import tiktokLogo from 'assets/logo/tiktok.png';

// Import preview components
import { FacebookPreview, InstagramPreview, TikTokPreview, LinkedInPreview } from './Previews';

interface MultiSelectProps {
  options: { id: string; name: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder: string;
}

const MultiSelect: React.FC<MultiSelectProps> = ({ options, selected, onChange, placeholder }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (optionId: string) => {
    const newSelected = selected.includes(optionId)
      ? selected.filter(item => item !== optionId)
      : [...selected, optionId];
    onChange(newSelected);
  };

  const getDisplayValue = () => {
    if (selected.length === 0) return placeholder;
    if (selected.length === 1) {
      const option = options.find(opt => opt.id === selected[0]);
      return option ? option.name : selected[0];
    }
    return `${selected.length} selected`;
  };

  return (
    <div className={classes.multiSelect}>
      <div
        className={classes.multiSelectTrigger}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{getDisplayValue()}</span>
        <FontAwesomeIcon icon={['fas', 'chevron-down']} className={cn(classes.chevron, isOpen && classes.open)} />
      </div>
      {isOpen && (
        <div className={classes.multiSelectDropdown}>
          {options.map(option => (
            <div
              key={option.id}
              className={cn(classes.multiSelectOption, selected.includes(option.id) && classes.selected)}
              onClick={() => toggleOption(option.id)}
            >
              <input
                type="checkbox"
                checked={selected.includes(option.id)}
                onChange={() => toggleOption(option.id)}
                className={classes.checkbox}
              />
              <span>{option.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface ShareModalProps {
  isOpen: boolean;
  handleClose: () => void;
  step: string;
  isLoading?: boolean;
  answerName?: string;
  onConnectAccounts?: () => void;
  onConnectMoreAccounts?: () => void;
  children?: React.ReactNode;
  videoThumbnail?: string;
  client?: any; // Will contain basic client data
  socialMediaService?: any; // SocialMediaService instance
  ayrshareProfileService?: any; // AyrshareProfileService instance
  onProfileUpdated?: (profileData: any) => void; // Callback when profile is refreshed
  onStepChange?: (newStep: string) => void; // Callback to change step
  onLoadingChange?: (isLoading: boolean) => void; // Callback to change loading state
  answerId?: number; // Answer ID for posting
  questionText?: string; // Question text for AI generation
  answerText?: string; // Answer text for AI generation
}

export default function ShareModal({
  isOpen,
  handleClose,
  step,
  isLoading = false,
  answerName,
  onConnectAccounts,
  onConnectMoreAccounts,
  children,
  videoThumbnail,
  client,
  socialMediaService,
  ayrshareProfileService,
  onProfileUpdated,
  onStepChange,
  onLoadingChange,
  answerId,
  questionText,
  answerText,
}: ShareModalProps) {
  const [selectedSocialMedia, setSelectedSocialMedia] = useState<string[]>([]);
  const [platformContents, setPlatformContents] = useState<{ [key: string]: string }>({});
  const [previewPlatform, setPreviewPlatform] = useState<string>('');
  const [isRefreshingProfile, setIsRefreshingProfile] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [postingPlatform, setPostingPlatform] = useState<string | null>(null);
  const [successfullyPostedPlatforms, setSuccessfullyPostedPlatforms] = useState<string[]>([]);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [generatingPlatform, setGeneratingPlatform] = useState<string | null>(null);
  const connectionWindowOpenedRef = useRef(false);

  // Get ayrshare profile from service
  const ayrshareProfile = ayrshareProfileService?.getCurrentProfile();

  // Check if social media accounts are already connected
  const hasConnectedAccounts = ayrshareProfileService?.hasConnectedAccounts() || false;

  // Get available social media platforms from connected accounts
  const getAvailablePlatforms = () => {
    if (!hasConnectedAccounts || !ayrshareProfile?.socialMediaAccounts) {
      return socialMediaOptions;
    }

    const connectedPlatforms = ayrshareProfile.socialMediaAccounts.map((account: any) => account.platform);
    return socialMediaOptions.filter(option => connectedPlatforms.includes(option.id));
  };

  // Handle connecting social media accounts
  const handleConnectAccounts = async () => {

    if (!socialMediaService || !ayrshareProfileService) {
      if (onConnectAccounts) {
        onConnectAccounts();
      }
      return;
    }

    if (!client?.id) {
      if (onConnectAccounts) {
        onConnectAccounts();
      }
      return;
    }

    try {
      let profileKey: string;

      // Check if ayrshareProfile already exists in the service
      const existingProfileKey = ayrshareProfileService.getProfileKey();
      if (existingProfileKey) {
        profileKey = existingProfileKey;
      } else {
        // Create new ayrshare profile first
        const profileResponse = await socialMediaService.createAyrshareProfile({
          title: `${client.name} Profile`,
          clientId: client.id
        });

        // The profile should now be updated in ayrshareProfileService automatically
        const updatedProfileKey = ayrshareProfileService.getProfileKey();
        if (!updatedProfileKey) {
          console.error('Failed to create ayrshare profile - no profileKey available after creation');
          if (onConnectAccounts) {
            onConnectAccounts();
          }
          return;
        }

        profileKey = updatedProfileKey;
      }

      const response = await socialMediaService.generateConnectUrl({
        profileKey: profileKey
      });

      if (response && response.url) {
        // Set flag to indicate connection window was opened
        connectionWindowOpenedRef.current = true;
        // Open the connection URL in a new window/tab
        window.open(response.url, '_blank', 'noopener,noreferrer');
      } else {
        console.error('Failed to generate connection URL - no URL in response');
        if (onConnectAccounts) {
          onConnectAccounts();
        }
      }
    } catch (error) {
      console.error('Error in handleConnectAccounts:', error);
      if (onConnectAccounts) {
        onConnectAccounts();
      }
    }
  };

  // Handle connecting more social media accounts (same logic as initial connection)
  const handleConnectMoreAccounts = async () => {
    // Use the same logic as handleConnectAccounts
    await handleConnectAccounts();
  };

  // Handle posting to social media
  const handlePost = async (platformId: string) => {
    if (!socialMediaService || !ayrshareProfileService || !answerId) {
      console.error('Missing socialMediaService, ayrshareProfileService, or answerId');
      return;
    }

    const profileKey = ayrshareProfileService.getProfileKey();
    if (!profileKey) {
      console.error('No profileKey available from ayrshareProfileService');
      return;
    }

    const content = platformContents[platformId];
    if (!content || content.trim() === '') {
      console.error('No content provided for platform:', platformId);
      return;
    }

    setIsPosting(true);
    setPostingPlatform(platformId);

    try {
      const response = await socialMediaService.createPost({
        post: content,
        platforms: [platformId],
        answerId: answerId,
        profileKey: profileKey
      });

      if (response) {
        // Show success toast
        toast.success('Post created successfully');

        // Add platform to successfully posted list
        setSuccessfullyPostedPlatforms(prev => [...prev, platformId]);
      } else {
        console.error('Failed to post to', platformId);
        toast.error('Failed to create post');
      }
    } catch (error) {
      console.error('Error posting to', platformId, ':', error);
      toast.error('Failed to create post');
    } finally {
      setIsPosting(false);
      setPostingPlatform(null);
    }
  };

  // Handle posting to all selected platforms
  const handlePostToAll = async () => {
    if (!socialMediaService || !ayrshareProfileService || !answerId || selectedSocialMedia.length === 0) {
      console.error('Missing socialMediaService, ayrshareProfileService, answerId, or no platforms selected');
      return;
    }

    const profileKey = ayrshareProfileService.getProfileKey();
    if (!profileKey) {
      console.error('No profileKey available from ayrshareProfileService');
      return;
    }

    // Get content for each platform, using the first platform's content as fallback
    const platformsWithContent = selectedSocialMedia.filter(platformId => {
      const content = platformContents[platformId];
      return content && content.trim() !== '';
    });

    if (platformsWithContent.length === 0) {
      console.error('No content provided for any selected platform');
      return;
    }

    setIsPosting(true);
    setPostingPlatform('bulk');

    try {
      // For now, post the same content to all platforms
      // In the future, you might want to use platform-specific content
      const firstPlatformContent = platformContents[platformsWithContent[0]];

      const response = await socialMediaService.createPost({
        post: firstPlatformContent,
        platforms: selectedSocialMedia,
        answerId: answerId,
        profileKey: profileKey
      });

      if (response) {
        // Show success toast
        toast.success('Post created successfully');

        // Add all selected platforms to successfully posted list
        setSuccessfullyPostedPlatforms(prev => [...prev, ...selectedSocialMedia]);
      } else {
        console.error('Failed to post to platforms');
        toast.error('Failed to create post');
      }
    } catch (error) {
      console.error('Error posting to platforms:', error);
      toast.error('Failed to create post');
    } finally {
      setIsPosting(false);
      setPostingPlatform(null);
    }
  };

  // Handle AI generation for social media post
  const handleGenerateWithAI = async (platformId: string) => {

    if (!socialMediaService || !questionText || !answerText || !client?.name) {
      console.log('Missing some or all required data for AI generation but proceeding...');
    }

    setIsGeneratingAI(true);
    setGeneratingPlatform(platformId);

    try {
      const response = await socialMediaService.generatePost({
        question: questionText,
        answer: answerText,
        cityName: client.name, // Using client name as cityName for now
        platform: platformId
      });

      if (response && response.postText) {
        // Update the platform content with the generated text
        setPlatformContents(prev => ({
          ...prev,
          [platformId]: response.postText
        }));

        // Auto-resize the textarea
        setTimeout(() => {
          const textarea = document.querySelector(`textarea[data-platform="${platformId}"]`) as HTMLTextAreaElement;
          if (textarea) {
            textarea.style.height = 'auto';
            const scrollHeight = textarea.scrollHeight;
            const minHeight = 120;
            const maxHeight = 300;
            const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
            textarea.style.height = `${newHeight}px`;
          }
        }, 0);

        toast.success('AI post generated successfully');
      } else {
        console.error('Failed to generate AI post - no postText in response');
        toast.error('Failed to generate AI post');
      }
    } catch (error: any) {
      console.error('Error generating AI post:', error);

      // Handle specific error messages from the API
      if (error?.response?.data?.error) {
        toast.error(`Failed to generate AI post: ${error.response.data.error}`);
      } else {
        toast.error('Failed to generate AI post');
      }
    } finally {
      setIsGeneratingAI(false);
      setGeneratingPlatform(null);
    }
  };

  const socialMediaOptions = [
    { id: 'facebook', name: 'Facebook' },
    { id: 'instagram', name: 'Instagram' },
    { id: 'tiktok', name: 'TikTok' },
    { id: 'linkedin', name: 'LinkedIn' },
  ];

  // Function to get the logo for each platform
  const getPlatformLogo = (platformId: string) => {
    switch (platformId) {
      case 'facebook':
        return facebookLogo;
      case 'instagram':
        return instagramLogo;
      case 'tiktok':
        return tiktokLogo;
      case 'linkedin':
        return linkedinLogo;
      default:
        return null;
    }
  };

  // Auto-resize textarea and handle platform-specific content
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>, platformId: string) => {
    const newContent = e.target.value;
    setPlatformContents(prev => ({
      ...prev,
      [platformId]: newContent
    }));

    // Reset height to auto to get the correct scrollHeight
    const textarea = e.target;
    textarea.style.height = 'auto';
    // Set height to scrollHeight, but respect min and max heights
    const scrollHeight = textarea.scrollHeight;
    const minHeight = 120; // Match CSS min-height
    const maxHeight = 300; // Match CSS max-height
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = `${newHeight}px`;
  };

  // Reset successfully posted platforms when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSuccessfullyPostedPlatforms([]);
      setPostingPlatform(null);
    }
  }, [isOpen]);

  // Set preview platform to first selected platform
  useEffect(() => {
    if (selectedSocialMedia.length > 0 && !previewPlatform) {
      setPreviewPlatform(selectedSocialMedia[0]);
    } else if (selectedSocialMedia.length === 0) {
      setPreviewPlatform('');
    }
  }, [selectedSocialMedia, previewPlatform]);

  // Function to refresh profile data and create missing social media accounts
  const refreshProfile = async () => {
    if (!ayrshareProfileService || !socialMediaService || !onProfileUpdated) {
      return;
    }

    const refId = ayrshareProfileService.getRefId();
    if (!refId) {
      return;
    }

    setIsRefreshingProfile(true);

    // Set loading state
    if (onLoadingChange) {
      onLoadingChange(true);
    }

    try {
      const profileData = await socialMediaService.getProfile(refId);

      if (profileData && profileData.profiles && profileData.profiles.length > 0) {
        const profile = profileData.profiles[0];
        const activeSocialAccounts = profile.activeSocialAccounts || [];

        // Get existing social media accounts from ayrshareProfileService
        const currentProfile = ayrshareProfileService.getCurrentProfile();
        const existingSocialAccounts = currentProfile?.socialMediaAccounts || [];
        const existingPlatforms = existingSocialAccounts.map((account: any) => account.platform);

        // Find platforms that need to be created in our database
        const platformsToCreate = activeSocialAccounts.filter((platform: string) =>
          !existingPlatforms.includes(platform)
        );

        // Create social media accounts for new platforms
        if (platformsToCreate.length > 0 && currentProfile) {

          // Create accounts sequentially to ensure profile updates are applied in order
          for (const platform of platformsToCreate) {
            try {
              const result = await socialMediaService.createSocialMediaAccount({
                clientId: client.id,
                platform: platform,
                ayrshareProfileId: currentProfile.id
              });
            } catch (error) {
              console.error(`Failed to create social media account for ${platform}:`, error);
            }
          }
        }

        // Call the profile updated callback
        onProfileUpdated(profileData);

        // Navigate to shareVideo step if we have connected accounts
        if (activeSocialAccounts.length > 0 && onStepChange) {
          onStepChange('shareVideo');
        }
      }
    } catch (error) {
      console.error('Error refreshing profile:', error);
    } finally {
      setIsRefreshingProfile(false);
      // Clear loading state
      if (onLoadingChange) {
        onLoadingChange(false);
      }
    }
  };

  // Page visibility detection to refresh profile when user returns
  useEffect(() => {
    if (!isOpen) return;

    const handleVisibilityChange = () => {
      // Only refresh if the modal is open, a connection window was opened, and the page becomes visible
      if (!document.hidden && connectionWindowOpenedRef.current) {
        connectionWindowOpenedRef.current = false; // Reset the flag
        refreshProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isOpen, ayrshareProfileService, socialMediaService, onProfileUpdated]);

  const renderStepContent = () => {
    if (children) {
      return children;
    }

    // Default content based on step if no children provided
    switch (step) {
      case 'connectAccounts':
        return (
          <div className={classes.connectAccountsContent}>
            {isLoading ? (
              <>
                <div className={classes.videoThumbnailContainer}>
                  <div className={classes.blackDiv}>
                    {videoThumbnail && (
                      <div className={classes.thumbnailWrapper}>
                        <img
                          src={videoThumbnail}
                          alt="Video thumbnail"
                          className={classes.videoThumbnail}
                        />
                      </div>
                    )}
                  </div>
                </div>
                <p className={classes.stepDescription}>
                  Checking to make sure your accounts are connected...
                </p>
                <div className={classes.spinner}></div>
              </>
            ) : (
              <>
                <div className={classes.videoThumbnailContainer}>
                  <div className={classes.blackDiv}>
                    {videoThumbnail && (
                      <div className={classes.thumbnailWrapper}>
                        <img
                          src={videoThumbnail}
                          alt="Video thumbnail"
                          className={classes.videoThumbnail}
                        />
                      </div>
                    )}
                  </div>
                </div>
                <h2 className={classes.getStartedTitle}>Get Started!</h2>
                <button
                  className={classes.linkAccountsButton}
                  onClick={handleConnectAccounts}
                >
                  Link your accounts.
                </button>
                <div className={classes.divider}></div>
                <p className={classes.connectDescription}>
                  Once connected, you'll be able to create your post and share.
                </p>
              </>
            )}
          </div>
        );
      case 'shareVideo':
        return (
          <div className={classes.shareVideoContent}>
            <div className={classes.shareContainer}>
              <div className={classes.leftColumn}>
                <h3 className={classes.columnTitle}>Create a Social Post</h3>
                <MultiSelect
                  options={getAvailablePlatforms()}
                  selected={selectedSocialMedia}
                  onChange={setSelectedSocialMedia}
                  placeholder="Social Media Accounts"
                />

                {selectedSocialMedia.length > 0 && (
                  <div className={classes.platformContainersWrapper}>
                    {selectedSocialMedia.map((platformId, index) => {
                      const platform = getAvailablePlatforms().find(opt => opt.id === platformId);
                      const logo = getPlatformLogo(platformId);
                      return platform ? (
                        <div key={platformId}>
                          <div className={classes.platformContainer}>
                            {logo && (
                              <img
                                src={logo}
                                alt={`${platform.name} logo`}
                                className={classes.platformLogo}
                              />
                            )}
                            <div className={classes.textboxContainer}>
                              <textarea
                                className={classes.textbox}
                                data-platform={platformId}
                                placeholder={`Write your ${platform.name} post content here...`}
                                value={platformContents[platformId] || ''}
                                onChange={(e) => handleTextareaChange(e, platformId)}
                                onFocus={() => setPreviewPlatform(platformId)}
                              />
                              <button
                                className={classes.generateButton}
                                onClick={() => {
                                  handleGenerateWithAI(platformId);
                                }}
                                disabled={isGeneratingAI}
                              >
                                {generatingPlatform === platformId ? (
                                  <>
                                    <FontAwesomeIcon icon={faSpinner} spin className={classes.buttonSpinner} />
                                    Generating...
                                  </>
                                ) : (
                                  'Generate with AI'
                                )}
                              </button>
                            </div>
                            <button
                              className={cn(
                                classes.platformPostButton,
                                successfullyPostedPlatforms.includes(platformId) && classes.successButton
                              )}
                              onClick={() => handlePost(platformId)}
                              disabled={isPosting || !platformContents[platformId]?.trim() || !answerId || !ayrshareProfileService?.getProfileKey() || successfullyPostedPlatforms.includes(platformId)}
                            >
                              {successfullyPostedPlatforms.includes(platformId) ? (
                                `Successfully posted to ${platform.name}`
                              ) : postingPlatform === platformId ? (
                                <>
                                  <FontAwesomeIcon icon={faSpinner} spin className={classes.buttonSpinner} />
                                  Posting...
                                </>
                              ) : (
                                `Post to ${platform.name}`
                              )}
                            </button>
                            <button
                              className={classes.scheduleButton}
                              onClick={() => {
                                // TODO: Implement schedule functionality
                                console.log('Schedule post for', platformId);
                              }}
                              disabled={!platformContents[platformId]?.trim() || successfullyPostedPlatforms.includes(platformId)}
                            >
                              Schedule post
                            </button>
                          </div>
                          {index < selectedSocialMedia.length - 1 && (
                            <div className={classes.platformDivider}></div>
                          )}
                        </div>
                      ) : null;
                    })}
                  </div>
                )}

                {selectedSocialMedia.length > 1 && (
                  <button
                    className={classes.bulkPostButton}
                    onClick={handlePostToAll}
                    disabled={isPosting || selectedSocialMedia.length === 0 || !answerId || !ayrshareProfileService?.getProfileKey()}
                  >
                    {postingPlatform === 'bulk' ? (
                      <>
                        <FontAwesomeIcon icon={faSpinner} spin className={classes.buttonSpinner} />
                        Posting...
                      </>
                    ) : (
                      `Post to All Selected Platforms`
                    )}
                  </button>
                )}
              </div>

              <div className={classes.rightColumn}>
                <h3 className={classes.columnTitle}>Preview Social Post</h3>
                <div className={classes.rightColumnContent}>
                  <div className={classes.answerName}>
                    Answer: {answerName || 'Untitled Answer'}
                  </div>

                  {selectedSocialMedia.length > 0 && (
                    <div className={classes.previewsContainer}>
                      {selectedSocialMedia.includes('facebook') && (
                        <FacebookPreview
                          content={platformContents['facebook'] || ''}
                          videoThumbnail={videoThumbnail}
                        />
                      )}
                      {selectedSocialMedia.includes('instagram') && (
                        <InstagramPreview
                          content={platformContents['instagram'] || ''}
                          videoThumbnail={videoThumbnail}
                        />
                      )}
                      {selectedSocialMedia.includes('tiktok') && (
                        <TikTokPreview
                          content={platformContents['tiktok'] || ''}
                          videoThumbnail={videoThumbnail}
                        />
                      )}
                      {selectedSocialMedia.includes('linkedin') && (
                        <LinkedInPreview
                          content={platformContents['linkedin'] || ''}
                          videoThumbnail={videoThumbnail}
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <button
              className={classes.connectMoreButton}
              onClick={handleConnectMoreAccounts}
            >
              Have any other social media accounts? Connect them here
            </button>
          </div>
        );
      default:
        return (
          <div className={classes.stepContent}>
            <p>Please select a valid step</p>
          </div>
        );
    }
  };

  return isOpen ? (
    <Modal contentWrapperStyle={{ maxWidth: step === 'shareVideo' ? "940px" : "640px", padding: 0 }}>
      <div className={classes.shareModal}>
        {step === 'connectAccounts' || step === 'shareVideo' ? (
          <>
            <div className={classes.blueHeader}>
              <h2 className={classes.headerTitle}>Share to Social</h2>
              <button className={classes.headerCloseButton} onClick={handleClose}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            {renderStepContent()}
          </>
        ) : (
          <>
            <div className={classes.titleWithClose}>
              <h2 className={classes.title}>Share</h2>
              <button className={classes.closeButton} onClick={handleClose}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            {renderStepContent()}
          </>
        )}
      </div>
    </Modal>
  ) : null;
}
