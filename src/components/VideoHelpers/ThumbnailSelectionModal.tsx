import React, { useEffect, useState } from "react";
import { AnswerInterface, QuestionInterface } from "../../interfaces";
import { useVideoUploadContext } from "./VideoUpload/VideoUploadProvider";
import { useServiceContext } from "../../services/ServiceProvider";
import UploadThumbnail from "./UploadThumbnail";
import Button from "../../shared/Button";
import Modal from "../../shared/Modal";
import emptyThumbnailUrl from "./emptyThumbnailUrl";
import Thumbnail from "./VideoCapture/Thumbnail";

type Props = {
  onClose: () => void;
  question: QuestionInterface;
  answer?: AnswerInterface;
};
export default function ThumbnailSelectionModal({ onClose, answer }: Props) {
  const { setAnswer } = useVideoUploadContext();
  const { answersService } = useServiceContext();
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(
    null
  );

  const [availableThumbnailUrls, setAvailableThumbnailUrls] = useState<
    string[]
  >([]);

  useEffect(() => {
    if (!answer) {
      return;
    }
    function getThumbnailUrl(n: number) {
      return `https://files.repd.us/thumbnails/${
        answer?.videoUrl.split(/video_parts\/|raw\//)[1].split("/")[0]
      }-${n}.png`;
    }

    const thumbnailUrls = Array(10)
      .fill(0)
      .map((_, index) => getThumbnailUrl(index));

    const checkUrls = async () => {
      const results: string[] = (
        await Promise.all(
          thumbnailUrls.map(async (url) => {
            try {
              const response = await fetch(
                url,
                // `${answersService?.base}/video-proxy?url=${encodeURIComponent(
                //   url
                // )}`,
                { method: "HEAD" }
              );
              return response.ok ? url : "";
            } catch {
              return "";
            }
          })
        )
      ).filter((item) => !!item);
      if (
        answer.imageUrl &&
        answer.imageUrl !== emptyThumbnailUrl &&
        !results.find((r) => r === answer.imageUrl)
      ) {
        results.unshift(answer.imageUrl);
      }
      setAvailableThumbnailUrls(results);
      if (results.length) {
        if (answer.imageUrl && answer.imageUrl !== emptyThumbnailUrl) {
          const selected = results.find((r) => r === answer.imageUrl);
          if (selected) setSelectedThumbnail(selected);
        } else {
          setSelectedThumbnail(results[0]);
        }
      }
    };

    checkUrls();
  }, [answer]);
  const handleThumbnailClick = (thumbnail: string) => {
    setSelectedThumbnail(thumbnail);
  };

  async function handlePublish() {
    if (!selectedThumbnail) {
      return;
    }
    const updatedAnswer: any = { ...answer, imageUrl: selectedThumbnail };
    answersService?.updateAnswer(updatedAnswer).then(() => {
      setAnswer(updatedAnswer);
      onClose();
    });
  }

  return (
    <Modal contentWrapperStyle={{ maxWidth: "860px" }}>
      <div className="p-4 w-full">
        {selectedThumbnail ? (
          <div className="mb-4 flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 rounded-lg bg-gray-100 py-4">
            <img
              src={selectedThumbnail}
              alt="Video Thumbnail"
              className="h-64 mx-auto"
            />
          </div>
        ) : (
          <div className="mb-4 flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 rounded-lg bg-gray-100 p-4">
            <div className="text-center text-gray-500">
              <p>{availableThumbnailUrls.length > 0 ? 'Select a thumbnail from the list below' : 'Upload a thumbnail'}</p>
            </div>
          </div>
        )}
        <div className="flex gap-4 w-full overflow-x-scroll">
          {availableThumbnailUrls.map((url, index) => (
            <div>
              <Thumbnail
                selectedThumbnail={selectedThumbnail}
                url={url}
                handleThumbnailClick={handleThumbnailClick}
              />
            </div>
          ))}
          <UploadThumbnail
            setSelectedThumbnail={setSelectedThumbnail}
            selectedThumbnail={selectedThumbnail}
          />
        </div>
        <div className="flex justify-center gap-6">
          <Button text="Close" callback={onClose} />
          {selectedThumbnail && (
            <Button text="Set Thumbnail" callback={handlePublish} />
          )}
        </div>
      </div>
    </Modal>
  );
}
