import React, { useEffect, useRef, useState } from "react";
import { useReactMediaRecorder } from "react-media-recorder";
import { useVideoCapture } from "./videoCaptureContext";
import "./style.css";
import { useVideoUploadContext } from "../VideoUpload/VideoUploadProvider";
import { useServiceContext } from "services/ServiceProvider";
import FontSizeIcon from "assets/icons/font-size.svg";
import { cn } from "utils/utils";
import { useAudioDevices } from "hooks";
import AudioDeviceSelector from "../../AudioDeviceSelector";

const VideoRecorder: React.FC = () => {
  const {
    teleprompterNotes,
    capturedFile,
    setCapturedFile,
    setCaptureStatus,
    setCaptureOption,
    setDoneTelepromt,
  } = useVideoCapture();

  const { userService, adminStatsService } = useServiceContext();
  const {
    audioDevices,
    selectedDeviceId,
    selectDevice,
    isLoading: audioDevicesLoading,
    error: audioDevicesError,
    audioConstraints
  } = useAudioDevices();

  const { setVideoDuration } = useVideoUploadContext();
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const videoPreviewRef = useRef<HTMLVideoElement | null>(null);
  const [previewStream, setPreviewStream] = useState<MediaStream | null>(null);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [countdown, setCountdown] = useState<number | null>(null);
  const [recordStartTime, setRecordStartTime] = useState<number>(0);

  const isMobile = () => {
    return window.innerWidth <= 640;
  };

  const { status, clearBlobUrl, startRecording, stopRecording, mediaBlobUrl } =
    useReactMediaRecorder({
      video: true,
      audio: audioConstraints,
      onStop: (blobUrl, blob) => {
        const file = new File([blob], "recorded-video.mp4", {
          type: "video/mp4",
        });
        setCapturedFile(file);
        clearInterval(intervalId!);
      },
    });

  useEffect(() => {
    if (!mediaBlobUrl) {
      const getUserMedia = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: audioConstraints,
          });
          setPreviewStream(stream);
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
          }
        } catch (error) {
          console.error("Error accessing user media:", error);
        }
      };

      getUserMedia();
    }

    return () => {
      if (previewStream) {
        previewStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [mediaBlobUrl, audioConstraints]);

  useEffect(() => {
    if (videoRef.current && previewStream) {
      videoRef.current.srcObject = previewStream;
      videoRef.current
        .play()
        .catch((error) =>
          console.error("Error attempting to play video:", error)
        );
    }
  }, [previewStream]);

  useEffect(() => {
    if (status === "recording") {
      setRecordingTime(0);
      const id = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1);
      }, 1000);
      setIntervalId(id);
    } else if (status === "stopped") {
      clearInterval(intervalId!);
    }
  }, [status]);

  const handleStartRecording = () => {
    const here = Date.now();
    setRecordStartTime(here + 3000);
    setCountdown(3);
    adminStatsService?.trackEvent('QuestionsArchive', 'start_recording');

    const countdownInterval = setInterval(() => {
      setCountdown((prevCountdown) => {
        if (prevCountdown === 1) {
          clearInterval(countdownInterval);
          startRecording();
          setCountdown(null);
          setCaptureStatus("capturing");
          clearInterval(countdownInterval);
          return null;
        }
        return prevCountdown! - 1;
      });
    }, 1000);
  };

  const handleStopRecording = () => {
    const now = Date.now();
    const dur = (now - recordStartTime) / 1000;
    setVideoDuration(dur);
    adminStatsService?.trackEvent('QuestionsArchive', 'stop_recording');

    stopRecording();
    setCaptureStatus("completed");
  };

  const { setFile } = useVideoUploadContext();
  const handleUpload = () => {
    if (capturedFile) {
      setFile(capturedFile);
      adminStatsService?.trackEvent('QuestionsArchive', 'upload_recorded_video');
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };
  const [scrollSpeed, setScrollSpeed] = useState(userService?.user?.telepromptSpeed || "1.00");
  const [isTestingSpeed, setIsTestingSpeed] = useState(false);
  const [fontSize, setFontSize] = useState(2); // 1: Small, 2: Medium, 3: Large
  const telepromterSpeedOptions = [
    "0.05",
    "0.15",
    "0.25",
    "0.50",
    "0.75",
    "1.00",
    "1.50",
    "2.00",
  ];
  const teleprompterRef = useRef<any>(null);
  // useEffect(() => {
  //   let animationFrameId: number;
  //   let startTime: number;
  //   const totalScrollHeight =
  //     teleprompterRef.current.scrollHeight -
  //     teleprompterRef.current.clientHeight;
  //
  //   const scrollText = (timestamp: number) => {
  //     if (!startTime) startTime = timestamp;
  //     const elapsed = timestamp - startTime;
  //     const distance = (elapsed * Number(scrollSpeed)) / 100;
  //     teleprompterRef.current.scrollTop = distance % totalScrollHeight;
  //
  //     if (status === "recording" || isTestingSpeed) {
  //       animationFrameId = requestAnimationFrame(scrollText);
  //     } else {
  //       cancelAnimationFrame(animationFrameId);
  //     }
  //   };
  //
  //   if (status === "recording" || isTestingSpeed) {
  //     startTime = 0; // Reset start time when starting the scroll
  //     animationFrameId = requestAnimationFrame(scrollText);
  //   }
  //
  //   return () => cancelAnimationFrame(animationFrameId);
  // }, [status, scrollSpeed, isTestingSpeed]);

  const scrollCriteria: boolean =
    teleprompterRef.current && (isTestingSpeed || status === "recording");

  let scrollingInterval: number | null | any;

  const clearScroll = () => {
    setScroll(0);
    clearInterval(scrollingInterval);
    scrollingInterval = null;
  };

  const setScroll = (amount: number) => {
    if (!teleprompterRef.current) return;
    console.log({ amount });
    if (amount === 0 || (scrollCriteria && amount > 0))
      teleprompterRef.current.scrollTop = amount;
  };
  const handleScroll = () => {
    if (scrollCriteria) setScroll(teleprompterRef.current.scrollTop + 1);
    else clearScroll();
  };
  useEffect(() => {
    let scrollTimer: ReturnType<typeof setTimeout>;
    let scrollingInterval: ReturnType<typeof setInterval>;

    if (teleprompterRef) {
      if (scrollCriteria) {
        const speedMultiplier = parseFloat(scrollSpeed),
          speed =
            speedMultiplier <= 0.15
              ? 30 / speedMultiplier
              : 80 / speedMultiplier;

        scrollTimer = setTimeout(() => {
          if (scrollingInterval) clearScroll();
          if (scrollCriteria)
            scrollingInterval = setInterval(() => handleScroll(), speed);
        }, 1000);
      } else clearScroll();
    } else clearScroll();
    return () => {
      clearTimeout(scrollTimer);
      clearInterval(scrollingInterval);
    };
  }, [teleprompterRef, scrollCriteria, isTestingSpeed, scrollSpeed]);

  const changeScrollSpeedHandler = (e: any) => {
    setScrollSpeed(e.target.value);
    if (userService?.user) {
      const updatedUser = { ...userService?.user, telepromptSpeed: e.target.value };
      userService?.updateUser(updatedUser, () => { });
    }
  }

  useEffect(() => {
    setFontSize(isMobile() ? 2 : 1);
  }, []);

  return (
    <div className="w-full h-full">
      {teleprompterNotes && (
        <div className="flex flex-row-reverse gap-4 items-center mb-2">
          <div
            className="text-blue-700 cursor-pointer"
            onClick={() => setIsTestingSpeed(!isTestingSpeed)}
          >
            {isTestingSpeed ? "Stop Testing" : "Test Teleprompt Speed"}
          </div>

          <div>
            <select
              name="videoSpeed"
              className="bg-gray-200 appearance-none focus:ring-0 focus:outline-none rounded p-2"
              value={scrollSpeed}
              onChange={(e) => changeScrollSpeedHandler(e)}
            >
              {telepromterSpeedOptions.map((item, index) => (
                <option value={item} key={index}>
                  {" "}
                  {item}x{" "}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-5">
              <span className="text-lg font-bold relative top-[-4px]">a</span>
              <div className="slider-container">
                <input
                  id="fontSizeSlider"
                  type="range"
                  min="1"
                  max="3"
                  step="1"
                  value={fontSize}
                  onChange={(e) => setFontSize(parseInt(e.target.value, 10))}
                  className="slider"
                />
                <div className="slider-ticks">
                  <span className="tick"></span>
                  <span className="tick"></span>
                  <span className="tick"></span>
                </div>
              </div>
              <span className="text-lg font-bold relative top-[-2px]">A</span>
            </div>
          </div>
        </div>
      )}
      <div className="flex flex-col items-center justify-center bg-gray-200 p-6">
        <div className="relative w-full h-96 bg-black">
          {status === "recording" && (
            <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded">
              {formatTime(recordingTime)}
            </div>
          )}
          {status !== "stopped" && (
            <video
              ref={videoRef}
              className="w-full h-full"
              autoPlay
              muted
              playsInline
            />
          )}
          {status === "stopped" && mediaBlobUrl && (
            <video
              ref={videoPreviewRef}
              src={mediaBlobUrl}
              controls
              className="w-full h-full bg-black"
              autoPlay
              playsInline
            />
          )}
          {teleprompterNotes && (
            <div
              ref={teleprompterRef}
              className="bg-black/50 absolute inset-x-0 top-0 h-1/3 overflow-y-hidden text-white text-lg p-4 pointer-events-none leading-[2em]"
            >
              <div className="text-center">
                {teleprompterNotes.split("\n").map((line, index) => (
                  <p
                    key={index}
                    className={cn(
                      fontSize === 1 ? "mb-2" : fontSize === 2 ? "text-2xl mb-4" : "text-3xl mb-5"
                    )}
                  >
                    {line}
                  </p>
                ))}
              </div>
            </div>
          )}

          {countdown !== null && (
            <div className="absolute inset-0 flex items-center justify-center text-white text-6xl">
              {countdown}
            </div>
          )}
        </div>
        <div className="mt-4 flex max-sm:flex-col-reverse max-sm:gap-4 w-full justify-center items-center relative">
          <div className="flex max-sm:flex-1 items-center sm:absolute sm:left-0">
            <AudioDeviceSelector
              audioDevices={audioDevices}
              selectedDeviceId={selectedDeviceId}
              selectDevice={selectDevice}
              isLoading={audioDevicesLoading}
              error={audioDevicesError}
            />
          </div>
          <div className="flex max-sm:flex-1 space-x-4">
            {status !== "recording" && status !== "stopped" && (
              <button
                onClick={handleStartRecording}
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
              >
                Start Recording
              </button>
            )}
            {status === "recording" && (
              <button
                onClick={handleStopRecording}
                className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
              >
                Stop Recording
              </button>
            )}
            {status === "stopped" && (
              <>
                <button
                  onClick={() => {
                    setDoneTelepromt(false);
                    setCaptureOption(null);
                  }}
                  className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    clearBlobUrl();
                  }}
                  className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Redo
                </button>
                <button
                  onClick={handleUpload}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Upload Video
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoRecorder;
