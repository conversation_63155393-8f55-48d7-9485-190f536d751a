import { useMemo, useEffect, useState } from "react";
import { AnswerInterface } from "../../../interfaces";
import emptyThumbnailUrl from "../emptyThumbnailUrl";
import { VideoStepStatus } from "./VideoUploadProvider";

type Props = {
  answer?: AnswerInterface;
};

export default function useVideoStepStatus({ answer }: Props): VideoStepStatus {
  const [thumbnailsReady, setThumbnailsReady] = useState(false);

  // Check if thumbnails are available when answer changes
  useEffect(() => {
    if (!answer?.videoUrl) {
      setThumbnailsReady(false);
      return;
    }

    // Extract video ID from videoUrl to construct thumbnail URL
    const videoId = answer.videoUrl.split(/video_parts\/|raw\//)[1]?.split("/")[0];
    if (!videoId) {
      setThumbnailsReady(false);
      return;
    }

    let attempts = 0;
    const maxAttempts = 5; // 5 attempts

    // Check if at least the first thumbnail is available
    const checkThumbnailAvailability = () => {
      const thumbnailUrl = `https://files.repd.us/thumbnails/${videoId}-0.png`;

      // Create a hidden img element to test if the thumbnail loads
      const img = new Image();
      img.onload = () => {
        setThumbnailsReady(true);
      };
      img.onerror = () => {
        attempts++;
        if (attempts < maxAttempts) {
          // Try again in 2 seconds
          setTimeout(checkThumbnailAvailability, 2000);
        } else {
          // After 5 failed attempts, proceed to thumbnail screen anyway
          setThumbnailsReady(true);
        }
      };
      img.src = thumbnailUrl;
    };

    // Start checking immediately
    checkThumbnailAvailability();

    // Cleanup function to prevent memory leaks
    return () => {
      attempts = maxAttempts; // Stop any pending checks
    };
  }, [answer?.videoUrl]);

  return useMemo(() => {
    if (!answer) {
      return VideoStepStatus.upload;
    }
    if (answer?.mp4VideoStatus === "") {
      return VideoStepStatus.process;
    }
    if (!answer?.imageUrl || answer.imageUrl === emptyThumbnailUrl) {
      // Only proceed to thumbnail step if thumbnails are actually ready
      return thumbnailsReady ? VideoStepStatus.thumbnail : VideoStepStatus.process;
    }
    return VideoStepStatus.publish;
  }, [answer, thumbnailsReady]);
}
