import { FunctionComponent, useMemo } from "react";
import styles from "./EmailDetailsStats.module.scss";
import { useServiceContext } from "services/ServiceProvider";

export type EmailDetailsStatsType = {
  className?: string;
  data: any;
};

const EmailDetailsStats: FunctionComponent<EmailDetailsStatsType> = ({
  className = "",
  data
}) => {
  const { statService } = useServiceContext();

  const quartileData = useMemo(() => {
    return statService?.computedValues.quartileData
  }, [statService?.computedValues.quartileData]);

  const quartiles = [
    { label: '100%', value: quartileData?.find(item => item.answer_id === data.id)?.["75-100"] || 0 },
    { label: '75%', value: quartileData?.find(item => item.answer_id === data.id)?.["50-75"] || 0 },
    { label: '50%', value: quartileData?.find(item => item.answer_id === data.id)?.["25-50"] || 0 },
    { label: '25%', value: quartileData?.find(item => item.answer_id === data.id)?.["0-25"] || 0 },
  ];

  return (
    <div className={[styles.chartContainerParent, className].join(" ")}>
      {data?.videoUrl &&
        <div className={styles.chartContainer}>
          <div className={styles.chartHeader}>
            <div className={styles.quartileReporting}>Quartile Reporting</div>
          </div>
          <div className={styles.chartContent}>
            {quartiles.map((quartile, index) => (
              <div className="w-full" key={index}>
                <div className={styles.quartileRow}>
                  <div className={styles.cityLabelWrapper}>
                    <div className={styles.div}>{quartile.label}</div>
                  </div>
                  <div className={styles.cityBar} style={{ width: `${quartile.value}%` }} />
                  <div className={styles.cityValue}>
                    <b className={styles.cityPercentage}>{quartile.value}%</b>
                  </div>
                </div>
                {index < quartiles.length - 1 && (
                  <img
                    className={styles.nationalRowIcon}
                    loading="lazy"
                    alt=""
                    src="/analytics/chart/divider"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      }
      <div className={styles.chartContainer1}>
        <div className={styles.responseWrapper}>
          <div className={styles.quartileReporting}>Opens & Clicks</div>
        </div>
        <div className={styles.VideoFeedbackContainer}>
          <b className={styles.cityLabel}>N/A{/* {`${data?.opensPercentage}%`} */}</b>
        </div>
        <img
          className={styles.chartContainerChild}
          loading="lazy"
          alt=""
          src="/analytics/chart/divider"
        />
        <div className={styles.VideoFeedbackContainer}>
          <b className={styles.cityLabel}>N/A{/* {`${data?.clicksPercentage}%`} */}</b>
        </div>
      </div>
      <div className={styles.chartContainer2}>
        <div className={styles.avgCompletionRateWrapper}>
          <div className={styles.quartileReporting}>Open Rate</div>
        </div>
        <div className={styles.frameGroup}>
          <div className={styles.emptyRowParent}>
            <div className={styles.emptyRow}>
              <img
                className={styles.emptyRowChild}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <div className={styles.frameContainer}>
              <div className={styles.nationalLabelWrapper}>
                <div className={styles.nationalLabel}>
                  <div className={styles.nationalLabel1}>
                    <div className={styles.nationalLabel2}>
                      <b className={styles.b1}>N/A{/* {`${data?.opensPercentage}%`} */}</b>
                    </div>
                    <div className={styles.nationalBar}>
                      <div className={styles.nationalBar1} style={{ height: 0 /* data?.opensPercentage * 1.13 */ }} />
                    </div>
                  </div>
                  <div className={styles.nationalLabel1}>
                    <div className={styles.nationalLabel2}>
                      <b className={styles.nationalPercentage}>N/A{/* {`${data?.avgOpensPercentage}%`} */}</b>
                    </div>
                    <div className={styles.nationalBar}>
                      <div className={styles.nationalBar3} style={{ height: 0 /* data?.avgOpensPercentage * 1.13 */ }} />
                    </div>
                  </div>
                </div>
              </div>
              <img
                className={styles.secondNationalLabel}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <div className={styles.emptyRow1}>
              <img
                className={styles.emptyRowChild}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <div className={styles.emptyRow1}>
              <img
                className={styles.emptyRowChild}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <img
              className={styles.frameItem}
              loading="lazy"
              alt=""
              src="/analytics/chart/divider"
            />
          </div>
          <div className={styles.emptyCityParent}>
            <div className={styles.emptyCity}>100%</div>
            <div className={styles.emptyCity1}>75%</div>
            <div className={styles.emptyCity1}>50%</div>
            <div className={styles.emptyCity1}>25%</div>
            <div className={styles.emptyCity1}>0%</div>
          </div>
          <div className={styles.city}>City</div>
          <div className={styles.national}>National</div>
        </div>
      </div>
    </div>
  );
};

export default EmailDetailsStats;
