import { FunctionComponent, useCallback, useEffect, useMemo, useState } from "react";
import classes from "./Filter.module.scss";
import cn from "classnames";
import { Select, Checkbox, MenuItem, FormGroup, FormControlLabel, Radio } from "@material-ui/core";
import { STATE_OPTIONS } from "utils/constants";
import { useFilterStore } from "hooks/zustand/filterStore";
import { DatePickerWithRange } from "components/VoterAnalytics/DatePicker";
import { DateRange } from "react-day-picker";
import { useServiceContext } from "services/ServiceProvider";
import { useInvalidateQueriesOnFilterChange } from "hooks/reactQuery/useAnalyticsQueries";

export type FilterProps = {
  className?: string;
  isSuperAdmin?: boolean;

  /** Variant props */
  type?: string;
};

type FilterType = {
  name: string;
  value: string;
}

const Filter: FunctionComponent<FilterProps> = ({
  className = "",
  type = "Default",
  isSuperAdmin = false
}) => {
  const { adminStatsService } = useServiceContext();
  const TIME_FRAME_OPTIONS = useMemo(() => (
    [
      {
        name: "Today",
        value: "today"
      },
      {
        name: "Last 7 Days",
        value: "7d"
      },
      {
        name: "Last 30 Days",
        value: "30d"
      },
      {
        name: "Last 90 Days",
        value: "90d"
      },
      {
        name: "Year to Date",
        value: "ytd"
      },
      {
        name: "Past Year",
        value: "1y"
      },
      {
        name: "Past Two Years",
        value: "2y"
      },
      {
        name: "All Time",
        value: "all"
      },
    ]
  ), [])

  const [popupOpen, setPopupOpen] = useState(false);
  const [mobileLocationSelectOpen, setMobileLocationSelectOpen] = useState(false);
  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  })
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<FilterType | null>(null);
  const [selectedLocations, setSelectedLocations] = useState<boolean[]>(Array(STATE_OPTIONS.length).fill(false));
  const [timeFilterType, setTimeFilterType] = useState<"preset" | "range">("preset");

  const filterState = useFilterStore((state) => state);
  const { locationFilters, timeFilter, setLocationFilters, setTimeFilter } = filterState;

  const setDateHandler = (date: DateRange | undefined) => {
    setTimeFilterType("range");
    setDate(date);
  }

  const setTimeFrameHandler = (timeFrame: FilterType) => {
    setTimeFilterType("preset");
    setSelectedTimeFrame(timeFrame);
  }

  useEffect(() => {
    setSelectedTimeFrame(timeFilter);
    if (!timeFilter) {
      setDate({ from: undefined, to: undefined });
    }
  }, [timeFilter])

  useEffect(() => {
    // convert filter keys from zustand to array of booleans to be provided to select component
    const locationValues = STATE_OPTIONS.map(item => item.value);
    const locationFilterValues = locationFilters.map(item => item.value);
    const updatedSelectedLocations = Array(STATE_OPTIONS.length).fill(false);
    locationValues.forEach((item, i) => {
      if (locationFilterValues.includes(item)) {
        updatedSelectedLocations[i] = true;
      }
    })
    setSelectedLocations(updatedSelectedLocations)
  }, [locationFilters])

  const checkHandler = useCallback((index: number) => {
    const updatedLocations = [...selectedLocations];
    updatedLocations[index] = !updatedLocations[index];
    setSelectedLocations(updatedLocations);
  }, [selectedLocations])

  const getAllIndexes = (arr: Array<any>, val: any) => {
    let indexes = [], i;
    for (i = 0; i < arr.length; i++)
      if (arr[i] === val)
        indexes.push(i);
    return indexes;
  }

  const formattedLocations = useMemo(() => {
    const locationIndexes = getAllIndexes(selectedLocations, true);
    return locationIndexes.map(index => STATE_OPTIONS[index]);
  }, [selectedLocations])

  const clearAll = () => {
    setSelectedTimeFrame(null);
    setDate({ from: undefined, to: undefined });
    setSelectedLocations(Array(STATE_OPTIONS.length).fill(false));
    setLocationFilters([]);
    setTimeFilter(null);

    // Invalidate queries to refetch data with cleared filters
    invalidateAnalyticsQueries();

    // Track clear all filters
    adminStatsService?.trackEvent('VoterAnalytics', 'clear_filters');
  }

  const openMobileLocationSelect = useCallback(() => {
    setMobileLocationSelectOpen(true);
  }, []);

  const closeMobileLocationSelect = useCallback(() => {
    setMobileLocationSelectOpen(false);
  }, []);

  const isMobile = () => {
    return window.innerWidth <= 640;
  };

  const { invalidateAnalyticsQueries } = useInvalidateQueriesOnFilterChange();

  const doneHandler = () => {
    if (mobileLocationSelectOpen && isMobile()) {
      // When in mobile location selection view, just go back to main filter view
      setMobileLocationSelectOpen(false);
    } else {
      // Normal behavior for main filter view
      const newTimeFilter = timeFilterType === "preset" && selectedTimeFrame ? selectedTimeFrame : (date?.from && date?.to) ? { name: `${date.from.toDateString()} - ${date.to.toDateString()}`, value: `${date.from.toDateString()} - ${date.to.toDateString()}` } : null;

      setLocationFilters(formattedLocations);
      setTimeFilter(newTimeFilter);
      setPopupOpen(false);
      setMobileLocationSelectOpen(false);

      // Invalidate queries to refetch data with new filters
      invalidateAnalyticsQueries();

      // Track filter application
      adminStatsService?.trackEvent('VoterAnalytics', 'apply_filter');
    }
  }

  const renderMobileLocationSelect = () => {
    return (
      <div className={classes.mobileLocationSelectContainer}>
        <div className={classes.mobileLocationList}>
          {STATE_OPTIONS.map((item, i) => (
            <div className={classes.mobileLocationItem} key={item.name} onClick={() => checkHandler(i)}>
              <Checkbox
                disableRipple
                style={{ color: "#B8BFCA" }}
                classes={{ root: classes.checkboxRoot }}
                className={classes.checkbox}
                value={item.value}
                checked={selectedLocations[i]}
                size="small"
              />
              <span>{item.name}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={classes.filterContainer}>
      <button
        className={cn(classes.filter1, className)}
        data-type={type}
        onClick={() => {
          setPopupOpen(!popupOpen);
          adminStatsService?.trackEvent('VoterAnalytics', 'toggle_filter');
        }}
      >
        <div className={classes.filterIcons}>
          <img className={classes.fi3024539Icon} alt="" src="/analytics/svg-icons/preference-icon.svg" />
          <a className={cn(classes.filter, classes.hideOnMobile)}>Filter</a>
        </div>
      </button>
      <div className={cn(classes.shadow, popupOpen ? "block" : "hidden")} />
      <div className={cn(classes.filterPopup, !popupOpen && classes.hide, !isSuperAdmin && classes.narrow)}>
        {mobileLocationSelectOpen && isMobile() ? (
          <>
            {renderMobileLocationSelect()}
            <button onClick={doneHandler} className={classes.doneButton}>
              Back
            </button>
          </>
        ) : (
          <>
            <div className={classes.filterHeader}>
              <div>Filter</div>
              <div className={classes.clearAll} onClick={clearAll}>Clear All</div>
            </div>
            <div className={classes.filterSelects}>
              {isSuperAdmin && <FormGroup className={classes.flex1}>
                <Select
                  className={classes.filterSelect}
                  disableUnderline
                  multiple
                  displayEmpty
                  value={selectedLocations.filter((item: boolean) => item)}
                  renderValue={(selected: any) => selected.length ? `${selected.length} selected` : "Location(s)"}
                  classes={{ selectMenu: classes.selectMenu, select: classes.selectInput }}
                  onClick={isMobile() ? openMobileLocationSelect : undefined}
                  MenuProps={{
                    PaperProps: {
                      className: classes.selectMenu
                    },
                    anchorOrigin: {
                      vertical: "bottom",
                      horizontal: 'center'
                    },
                    transformOrigin: {
                      vertical: -50,
                      horizontal: 'center'
                    },
                    disableScrollLock: true,
                    style: { display: isMobile() ? 'none' : 'block' }
                  }}>
                  {STATE_OPTIONS.map((item, i) => (
                    <MenuItem value={item.name} className={classes.selectItem} key={item.name}>
                      <FormControlLabel control={
                        <Checkbox
                          disableRipple
                          style={{ color: "#B8BFCA" }}
                          classes={{ root: classes.checkboxRoot }}
                          className={classes.checkbox}
                          value={item.value}
                          checked={selectedLocations[i]}
                          onChange={(e) => checkHandler(i)}
                        />}
                        className={classes.checkboxLabel}
                        classes={{ label: classes.checkboxLabel }}
                        label={item.name}
                      />
                    </MenuItem>
                  ))}
                </Select>
              </FormGroup>}
              <FormGroup className={classes.flex1}>
                <Select
                  className={classes.filterSelect}
                  disableUnderline
                  displayEmpty
                  value={selectedTimeFrame}
                  renderValue={(selected: FilterType | any) => selected?.name ? selected.name.substring?.(0, 15) : "Time Frame"}
                  classes={{ selectMenu: classes.selectMenu, select: classes.selectInput }}
                  MenuProps={{
                    PaperProps: {
                      className: classes.selectMenu
                    },
                    anchorOrigin: {
                      vertical: "bottom",
                      horizontal: 'center'
                    },
                    transformOrigin: {
                      vertical: -50,
                      horizontal: 'center'
                    },
                    disableScrollLock: true,
                  }}>
                  {TIME_FRAME_OPTIONS.map((item) => (
                    <MenuItem value={item.name} className={classes.selectItem} key={item.value}>
                      <FormControlLabel control={
                        <Radio
                          style={{ color: "#B8BFCA" }}
                          classes={{ root: classes.checkboxRoot }}
                          className={classes.checkbox} />}
                        checked={item.value === selectedTimeFrame?.value}
                        onChange={() => setTimeFrameHandler(item)}
                        className={classes.checkboxLabel}
                        classes={{ label: classes.checkboxLabel }}
                        label={item.name}
                      />
                    </MenuItem>
                  ))}
                </Select>
              </FormGroup>
              <DatePickerWithRange date={date} setDate={setDateHandler} />
            </div>
            <button onClick={doneHandler} className={classes.doneButton}>Done</button>
          </>
        )}
      </div>
    </div>
  );
};

export default Filter;
