import { Dispatch, FunctionComponent, useMemo, useState } from "react";
import { motion } from "framer-motion";
import CategoryLinks from "components/CategoryLinks";
import QuestionItem from "components/QuestionItem";
import classes from "./Queries.module.scss";
import { TopCategoryInterface } from "interfaces";
import moment from "moment";
import { cn, numberWithCommas } from "utils/utils";
import * as interfaces from 'interfaces';
import { useUIStore } from "hooks/zustand/uiStore";
import { useServiceContext } from "services/ServiceProvider";

export type QueriesType = {
  className?: string;

  /** Variant props */
  type?: string;
  totalQueries: number;
  categoryData: TopCategoryInterface[];
  onAnswer: (id: string) => void;
  user?: interfaces.UserInterface;
  trendingStats: interfaces.TrendingComputedInterface;
};

const Queries: FunctionComponent<QueriesType> = ({
  className = "",
  type = "Default",
  totalQueries,
  categoryData,
  onAnswer,
  user,
  trendingStats
}) => {
  const [selectedCategoryName, setSelectedCategoryName] = useState<string>(categoryData?.[0]?.category);
  const [sortBy, setSortBy] = useState<"volume" | "sentiment">("volume");
  const uiType = useUIStore(state => state.uiType);
  const { adminStatsService, questionService, clientService } = useServiceContext();

  // Set the selectedCategoryName to the first category item
  useMemo(() => {
    setSelectedCategoryName(categoryData?.[0]?.category);
  }, [categoryData]);

  const getSentimentFace = (sentiment: number) => {
    if (!sentiment) return "/analytics/faces/neutral-face.svg";
    return Number(sentiment) > 2.33 ? "/analytics/faces/unhappy-face.svg" : Number(sentiment) > 1.66 ? "/analytics/faces/neutral-face.svg" : Number(sentiment) < 1.66 ? "/analytics/faces/happy-face.svg" : "/analytics/faces/neutral-face.svg"
  }

  const sentimentClickHandler = () => {
    setSortBy("sentiment");
    adminStatsService?.trackEvent('VoterAnalytics', 'sort_queries');
  };

  const volumeClickHandler = () => {
    setSortBy("volume");
    adminStatsService?.trackEvent('VoterAnalytics', 'sort_queries');
  };

  const sortedCategoryData = useMemo(() => {
    return categoryData.sort((a, b) => {
      if (sortBy === "volume") {
        return b.volumePercentage - a.volumePercentage;
      } else {
        return b.sentiment - a.sentiment;
      }
    });
  }, [sortBy, categoryData]);

  const selectedCategory = useMemo(() => {
    return sortedCategoryData.find((category) => category?.category?.match(selectedCategoryName) !== null);
  }, [selectedCategoryName, sortedCategoryData]);

  return (
    <motion.div
      initial={{ opacity: 0, x: 40 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: 0.5 }}
      className={[classes.queries1, className].join(" ")}
      data-type={type}
    >
      <div className={classes.frameParent}>
        <div className={classes.queriesTitleContainerParent}>
          <div className={classes.queriesTitleContainer}>
            <div className={classes.queries}>Queries</div>
          </div>
        </div>
        <div className={classes.totalVolumeContentParent}>
          <b className={classes.totalVolumeValue}>{numberWithCommas(totalQueries)}</b>
          <div className={classes.totalVolumeWrapper}>
            <div className={classes.totalVolume}>Total Volume</div>
          </div>
        </div>
        <div className={cn(classes.categoriesContentParent, uiType === "pdf" && "pr-4 justify-around")}>
          <div className={cn(classes.categoriesContent, classes.hideOnMobile)}>
            <div className={classes.categoriesHeader}>
              <div className={classes.metrics}>
                <div className={classes.topCategories}>Top Categories</div>
                <div onClick={sentimentClickHandler} className={classes.metricTypes}>
                  <div className={classes.sentiment}>Sentiment</div>
                  <div className={classes.metricIcons}>
                    <img
                      className={classes.sortIcon}
                      alt=""
                      src="/analytics/chart/dropdown-caret.svg"
                    />
                  </div>
                </div>
                <div onClick={volumeClickHandler} className={classes.metricTypes1}>
                  <div className={classes.volume}>Volume</div>
                  <div className={classes.sortIconContainer}>
                    <img
                      className={classes.sortIcon}
                      alt=""
                      src="/analytics/chart/dropdown-caret.svg"
                    />
                  </div>
                </div>
              </div>
              <div className={classes.categoryList}>
                {sortedCategoryData?.slice(0, 6)?.map((item, i) => {
                  return <CategoryLinks
                    key={i}
                    onClick={() => {
                      setSelectedCategoryName(item.category);
                      adminStatsService?.trackEvent('VoterAnalytics', 'select_category');
                    }}
                    selected={item.category === selectedCategoryName}
                    categoryName={item?.category}
                    faces={getSentimentFace(item?.sentiment)}
                    sentiment={item.sentiment ? `${Math.round((3 - item.sentiment) / 2 * 100)}%` : "N/A"}
                    volume={item?.volumePercentage}
                    volumeDiff={item?.volumeDiffPercentage}
                  />
                })}
              </div>
            </div>
          </div>

          <CategoryLinks
            onClick={() => {
              setSelectedCategoryName(sortedCategoryData[0]?.category);
              adminStatsService?.trackEvent('VoterAnalytics', 'select_category_mobile');
            }}
            selected={sortedCategoryData[0]?.category === selectedCategoryName}
            categoryName={sortedCategoryData[0]?.category}
            faces={getSentimentFace(sortedCategoryData[0]?.sentiment)}
            sentiment={sortedCategoryData[0]?.sentiment ? `${Math.round((3 - sortedCategoryData[0]?.sentiment) / 2 * 100)}%` : "N/A"}
            volume={sortedCategoryData[0]?.volumePercentage}
            volumeDiff={sortedCategoryData[0]?.volumeDiffPercentage}
            mobile
            className={classes.showOnMobile} />

          {
            uiType === "pdf" ?
              <div className={cn(classes.categoriesContent, classes.hideOnMobile)}>
                <div className={classes.categoriesHeader}>
                  <div className={classes.metrics}>
                    <div className={classes.topCategories}>Top Categories</div>
                    <div onClick={sentimentClickHandler} className={classes.metricTypes}>
                      <div className={classes.sentiment}>Sentiment</div>
                      <div className={classes.metricIcons}>
                        <img
                          className={classes.sortIcon}
                          alt=""
                          src="/analytics/chart/dropdown-caret.svg"
                        />
                      </div>
                    </div>
                    <div onClick={volumeClickHandler} className={classes.metricTypes1}>
                      <div className={classes.volume}>Volume</div>
                      <div className={classes.sortIconContainer}>
                        <img
                          className={classes.sortIcon}
                          alt=""
                          src="/analytics/chart/dropdown-caret.svg"
                        />
                      </div>
                    </div>
                  </div>
                  <div className={classes.categoryList}>
                    {sortedCategoryData?.slice(6, 12)?.map((item, i) => {
                      return <CategoryLinks
                        key={i}
                        onClick={() => {
                          setSelectedCategoryName(item.category);
                          adminStatsService?.trackEvent('VoterAnalytics', 'select_category_pdf');
                        }}
                        selected={item.category === selectedCategoryName}
                        categoryName={item?.category}
                        faces={getSentimentFace(item?.sentiment)}
                        sentiment={item.sentiment ? `${Math.round((3 - item.sentiment) / 2 * 100)}%` : "N/A"}
                        volume={item?.volumePercentage}
                        volumeDiff={item?.volumeDiffPercentage}
                      />
                    })}
                  </div>
                </div>
              </div> : <div className={classes.questions}>
                <div className={classes.topQuestionsAboutContainer1}>
                  <span className={classes.topQuestionsAboutContainer}>
                    <span>{`Top Queries about `}</span>
                    <b>{selectedCategoryName}</b>
                  </span>
                </div>
                <div className={classes.questionList}>
                  <div className={classes.question} data-acc-group>
                    {(!selectedCategory?.questions || selectedCategory.questions.length === 0) && (
                      <div className={classes.noQuestions}>
                        <div className={classes.noQuestionsText}>No Questions to display in this category</div>
                      </div>
                    )}
                    {selectedCategory?.questions?.sort((a, b) => {
                      const timeA = Date.parse(a.created_at) ? new Date(a.created_at).getTime() : 0;
                      const timeB = Date.parse(b.created_at) ? new Date(b.created_at).getTime() : 0;
                      return timeB - timeA;
                    })
                      .map((item) => {
                        return (
                          <QuestionItem
                            key={item.id}
                            question={item.text}
                            onAnswer={() => onAnswer(item.id)}
                            email={item.email}
                            dateString={moment(item.created_at).format("MMM Do YY").toUpperCase()}
                            user={user}
                            questionData={item}
                            questionService={questionService}
                            clientService={clientService}
                          />
                        );
                      })}
                  </div>
                </div>
              </div>
          }
        </div>
      </div>
    </motion.div>
  );
};

export default Queries;
