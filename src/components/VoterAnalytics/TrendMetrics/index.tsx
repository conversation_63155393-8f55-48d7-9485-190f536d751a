import { FunctionComponent } from "react";
import styles from "./TrendMetrics.module.scss";

export type TrendMetricsType = {
  className?: string;
  value?: string;
};

const TrendMetrics: FunctionComponent<TrendMetricsType> = ({
  className = "",
  value,
}) => {
  return (
    <div className={[styles.trendMetrics, className].join(" ")}>
      <b className={styles.placeholder}>{value}</b>
      <div className={styles.trendSummary}>
        <div className={styles.totalVolume}>Total Volume</div>
      </div>
    </div>
  );
};

export default TrendMetrics;
