import { useQuery, useQueryClient } from '@tanstack/react-query';
import { StatService } from 'services';
import * as interfaces from 'interfaces';
import { FilterType } from 'hooks/zustand/filterStore';

// Query keys
export const QUERY_KEYS = {
  STATS: 'stats',
  ANALYTICS: 'analytics',
  QUESTION_ANALYTICS: 'questionAnalytics',
  TRENDING_ANALYTICS: 'trendingAnalytics',
  VIDEOS: 'videos',
  EMAIL_ANALYTICS: 'emailAnalytics',
  EMAIL_CHART_DATA: 'emailChartData',
  EMAIL_BULK_SEND_DATA: 'emailBulkSendData',
};

// Fetch stats
export const useStats = (service: StatService) => {
  return useQuery({
    queryKey: [QUERY_KEYS.STATS],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        service.fetch((stats: any) => {
          resolve(stats);
        });
      });
    },
  });
};

// Fetch analytics
export const useAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      return new Promise<interfaces.EngagementAnalyticsInterface>((resolve) => {
        // Temporarily override the service's clientId if provided
        const originalClientId = service.clientId;
        if (clientId) {
          service.clientId = clientId;
        }

        service.fetchAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (data: interfaces.EngagementAnalyticsInterface) => {
            // Restore original clientId
            service.clientId = originalClientId;
            resolve(data);
          }
        );
      });
    },
  });
};

// Fetch question analytics
export const useQuestionAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.QUESTION_ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        // Temporarily override the service's clientId if provided
        const originalClientId = service.clientId;
        if (clientId) {
          service.clientId = clientId;
        }

        service.fetchQuestionAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (questionData: any) => {
            // Restore original clientId
            service.clientId = originalClientId;
            resolve(questionData);
          }
        );
      });
    },
  });
};

// Fetch trending analytics
export const useTrendingAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TRENDING_ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        // Temporarily override the service's clientId if provided
        const originalClientId = service.clientId;
        if (clientId) {
          service.clientId = clientId;
        }

        service.fetchTrendingAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (trendingData: any) => {
            // Restore original clientId
            service.clientId = originalClientId;
            resolve(trendingData);
          }
        );
      });
    },
  });
};

// Fetch videos
export const useVideos = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.VIDEOS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      return new Promise<{
        videos: interfaces.VideoStatInterface[],
        videoFeedbackStats: interfaces.VideoStatInterfaceVideo['videoFeedbackStats'][],
        completionRateStats: interfaces.VideoStatInterfaceVideo['completionRateStats'][],
        nationalCompletionRate: { average_completion_rate: number }[],
        videoPlayStats: interfaces.VideoPlayStats[]
      }>((resolve) => {
        // Temporarily override the service's clientId if provided
        const originalClientId = service.clientId;
        if (clientId) {
          service.clientId = clientId;
        }

        service.getVideos(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (data: any) => {
            // Restore original clientId
            service.clientId = originalClientId;
            resolve(data);
          }
        );
      });
    },
  });
};

// Fetch email analytics
export const useEmailAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.EMAIL_ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        // Temporarily override the service's clientId if provided
        const originalClientId = service.clientId;
        if (clientId) {
          service.clientId = clientId;
        }

        service.fetchEmailAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (emailData: any) => {
            // Restore original clientId
            service.clientId = originalClientId;
            resolve(emailData);
          }
        );
      });
    },
  });
};

// Fetch email chart data
export const useEmailChartData = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.EMAIL_CHART_DATA, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      // Temporarily override the service's clientId if provided
      const originalClientId = service.clientId;
      if (clientId) {
        service.clientId = clientId;
      }

      const result = service.fetchEmailChartData(
        timeFilter,
        locationFilters,
        isSuperAdmin
      );

      // Restore original clientId
      service.clientId = originalClientId;
      return result;
    },
  });
};

// Fetch email bulk send data
export const useEmailBulkSendData = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean,
  clientId?: string
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.EMAIL_BULK_SEND_DATA, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin, clientId],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        // Temporarily override the service's clientId if provided
        const originalClientId = service.clientId;
        if (clientId) {
          service.clientId = clientId;
        }

        service.fetchEmailBulkSendData(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (bulkSendData: any) => {
            // Restore original clientId
            service.clientId = originalClientId;
            resolve(bulkSendData);
          }
        );
      });
    },
  });
};

// Utility function to invalidate queries when filters change
export const useInvalidateQueriesOnFilterChange = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAnalyticsQueries: () => {
      // Use predicate function to invalidate all queries that start with these keys
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey[0];
          return [
            QUERY_KEYS.ANALYTICS,
            QUERY_KEYS.QUESTION_ANALYTICS,
            QUERY_KEYS.TRENDING_ANALYTICS,
            QUERY_KEYS.VIDEOS,
            QUERY_KEYS.EMAIL_ANALYTICS,
            QUERY_KEYS.EMAIL_CHART_DATA,
            QUERY_KEYS.EMAIL_BULK_SEND_DATA
          ].includes(queryKey as string);
        }
      });
    },
    // Alternative method to remove all analytics queries from cache entirely
    removeAnalyticsQueries: () => {
      queryClient.removeQueries({
        predicate: (query) => {
          const queryKey = query.queryKey[0];
          return [
            QUERY_KEYS.ANALYTICS,
            QUERY_KEYS.QUESTION_ANALYTICS,
            QUERY_KEYS.TRENDING_ANALYTICS,
            QUERY_KEYS.VIDEOS,
            QUERY_KEYS.EMAIL_ANALYTICS,
            QUERY_KEYS.EMAIL_CHART_DATA,
            QUERY_KEYS.EMAIL_BULK_SEND_DATA
          ].includes(queryKey as string);
        }
      });
    }
  };
};
