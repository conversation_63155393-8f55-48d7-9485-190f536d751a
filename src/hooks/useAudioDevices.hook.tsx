import { useState, useEffect, useCallback, useMemo } from 'react';

interface AudioDevice {
  deviceId: string;
  label: string;
}

export const useAudioDevices = () => {
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(
    () => localStorage.getItem('selectedAudioDevice')
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getAudioDevices = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // We need to get user permission before enumerating devices
      await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices
        .filter((device) => device.kind === 'audioinput')
        .map((device) => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(-4)}`,
        }));
      
      setAudioDevices(audioInputs);
    } catch (err) {
      setError('Could not access audio devices. Please check permissions.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    getAudioDevices();
    navigator.mediaDevices.addEventListener('devicechange', getAudioDevices);
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getAudioDevices);
    };
  }, [getAudioDevices]);

  useEffect(() => {
    if (audioDevices.length > 0) {
      const isValidSelection = audioDevices.some(d => d.deviceId === selectedDeviceId);
      if (!selectedDeviceId || !isValidSelection) {
        const newDeviceId = audioDevices[0].deviceId;
        setSelectedDeviceId(newDeviceId);
      }
    }
  }, [audioDevices, selectedDeviceId]);

  useEffect(() => {
    if (selectedDeviceId) {
      localStorage.setItem('selectedAudioDevice', selectedDeviceId);
    }
  }, [selectedDeviceId]);

  const selectDevice = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
  };

  const audioConstraints = useMemo(() => {
    return selectedDeviceId ? { deviceId: { exact: selectedDeviceId } } : true;
  }, [selectedDeviceId]);

  return {
    audioDevices,
    selectedDeviceId,
    selectDevice,
    isLoading,
    error,
    audioConstraints,
  };
};
