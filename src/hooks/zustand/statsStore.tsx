import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import * as interfaces from '../../interfaces';

interface StatsState {
  computedValues: interfaces.ComputedAnalyticsInterface;
  updateComputedValues: (values: interfaces.ComputedAnalyticsInterface) => void;
}

export const useStatsStore = create<StatsState>()(
  devtools(
    (set) => ({
      computedValues: {
        trendingData: {
          questions: [],
          queries: []
        },
        totalEngagements: null,
        totalTraffic: null,
        engagementRate: null,
        timeSaved: null,
        totalVideoViews: 0,
        averageWatchTime: "0:00",
        completedWatchRate: "0",
        deliveredEmails: 0,
        openedEmails: 0,
        clickedEmails: 0,
        openedEmailsPercentage: 0,
        clickedEmailsPercentage: 0,
        totalQueries: 0,
        trendingQueries: 0,
        trendingQuestions: 0,
        topCategories: [],
        bulkSendData: [],
        sentimentData: [],
        aiQueriesSentimentData: [],
        quartileData: [],
        completedWatchRates: {}
      },
      updateComputedValues: (values: interfaces.ComputedAnalyticsInterface) => {
        set({ computedValues: values });
      },
    })
  )
);
