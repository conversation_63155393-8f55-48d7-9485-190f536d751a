export interface CategoryStatInterface {
    category: string;
    amount: string;
}

export interface LocationStatInterface {
    zip: string;
    amount: string;
}

export interface NumberStatInterface {
    days: string;
    amount: string;
}

export interface ChartDataInterface {
    day: string;
    amount: number;
}

export interface VideoStatInterfaceVideo {
    videos: VideoStatInterface[];
    videoFeedbackStats: {
        id: number;
        likes: number;
        dislikes: number;
    };
    completionRateStats: {
        id: number;
        completion_rate: number;
    }
}

export interface VideoPlayStats {
    created_at: string;
    event_action: string;
    answer_id: string;
    video_duration: number;
    user: string;
}

export interface VideoStatInterface {
    title: string | undefined;
    thumbnailUrl: string | undefined;
    description: string;
    views: string | number;
    createdAt: string;
    category: string;
    id: string;
    days: string;
    amount: number;
    duration: string | number;
    thumbnail: string;
    question: string;
    mp4VideoStatus: string;
    videoFeedbackStats?: VideoStatInterfaceVideo['videoFeedbackStats'];
    completionRateStats?: VideoStatInterfaceVideo['completionRateStats'];
    videoPlayStats?: VideoPlayStats[];
    nationalCompletionRate?: number;
}

export interface TranscriptInterface {
    id: number;
    text: string;
    name: string;
}

export interface NgpVanUserStatInterface {
    savedList: string;
    savedListName: string;
    userName: string;
    userEmail: string;
    opensTotal: number;
    opensUnique: number;
    ctaTotal: number;
    clicksUnique: number;
    unsubscribes: number;
    donations: number;
}

export interface NgpVanEmailStatInterface {
    date: string;
    sends: string | null;
    question: string;
    emails: NgpVanUserStatInterface[];
}

export interface StatsInterface {
    categories: CategoryStatInterface[];
    locations: LocationStatInterface[];
    questions: NumberStatInterface | null;
    votes: NumberStatInterface | null;
    engagement: ChartDataInterface[];
    videos: VideoStatInterface[];
    emails: NgpVanEmailStatInterface[];
}

// Artificial

export interface NgpVanStatDisplayInterface {
    opensTotal: number;
    opensUnique: number;
    ctaTotal: number;
    clicksUnique: number;
    unsubscribes: number;
    donations: number;
}

export interface StatEventInterface {
}

export interface EngagementAnalyticsInterface {
    analytics: StatEventInterface[],
    mobileDesktop: [],
    referralPercentage: [],
    engagementsChart: [],
    aiQueriesChart: [],
    sentiments: SentimentAnalyticsInterface[],
    aiQueriesSentiments: SentimentAnalyticsInterface[],
}

export interface SentimentAnalyticsInterface {
    date: string;
    happy: number;
    neutral: number;
    unhappy: number;
}

export interface SentimentComputedInterface {
    date: string;
    happyPercentage: number;
    neutralPercentage: number;
    unhappyPercentage: number;
}

export interface TopCategoryInterface {
    category: string;
    questions: { text: string; email: string, created_at: string, id: string }[];
    sentiment: number;
    count: number;
    volumeDiffPercentage: number;
    volumePercentage: number;
}

export interface CategoryAnalyticsInterface {
    category: string;
    current_period_count: number;
    previous_period_count: number;
    sentiment: number;
    questions: { text: string; email: string, created_at: string, id: number }[];
}

export interface TrendingCategoryAnalyticsInterface {
    source: string;
    category: string;
    question_count: string | number;
    average_sentiment: number;
}

export interface TrendingComputedInterface {
    questions: {
        category: string;
        sentiment: number;
        question_count: number | string;
    }[];
    queries: {
        category: string;
        sentiment: number;
        question_count: number | string;
    }[];
}

export interface VideoQuartileDataInterface {
    answer_id: string;
    "0-25": number;
    "25-50": number;
    "50-75": number;
    "75-100": number;
    totalPlays: number;
    expectedPlays: number;
}

export interface ComputedAnalyticsInterface {
    totalEngagements: number | null;
    totalTraffic: number | null;
    engagementRate: number | null;
    timeSaved: string | null;
    totalVideoViews: number;
    averageWatchTime: string;
    completedWatchRate: string;
    deliveredEmails: number;
    openedEmails: number;
    clickedEmails: number;
    openedEmailsPercentage: number;
    clickedEmailsPercentage: number;
    totalQueries: number;
    trendingQueries: number;
    trendingQuestions: number;
    topCategories: TopCategoryInterface[];
    bulkSendData: BulkSendComputedInterface[];
    trendingData: TrendingComputedInterface;
    sentimentData: SentimentComputedInterface[];
    aiQueriesSentimentData: SentimentComputedInterface[];
    quartileData: VideoQuartileDataInterface[];
    completedWatchRates: { [videoId: string]: string };
}

export interface ChartDataInterface {
    opens: number;
    deliveries: number;
    created_at: string;
}

export interface BulkSendInterface {
    id: string;
    created_at: string;
    subject: string;
    saved_list_name: string;
    delivered: number;
    user_count: number;
    opens: number;
    clicks: number;
    unsubscribes: number;
    avgOpenRate: number;
    blocks: number;
    question_created_at: string;
    title: string;
    videoUrl: string;
    thumbnailUrl: string;
    category: string;
    views: number;
}

// Simplified interface - only keeping properties actually used on Emails page
export interface BulkSendComputedInterface {
    id: string;
    date: string;
    created_at: string;
    subject: string;
    saved_list_name: string;
    delivered: number;
    user_count: number;
    opens: number;
    clicks: number;
    unsubscribes: number;
    blocks: number;
    avgOpenRate: number;
    question_created_at: string;
    title: string;
    videoUrl: string;
    thumbnailUrl: string;
    category: string;
    views: number;
}

// Full interface with all computed properties (commented out as no longer needed)
/*
export interface BulkSendComputedInterfaceFull {
    unsubscribesString: string;
    clicksString: string;
    opensString: string;
    id: string;
    date: string;
    created_at: string;
    subject: string;
    saved_list_name: string;
    delivered: number;
    deliveredPercentage: number;
    user_count: number;
    opens: number;
    opensPercentage: number;
    clicks: number;
    clicksPercentage: number;
    unsubscribes: number;
    blocks: number;
    unsubscribesPercentage: number;
    avgOpenRate: number;
    avgOpensPercentage: number;
    blocksPercentage: number;
    question_created_at: string;
    title: string;
    videoUrl: string;
    thumbnailUrl: string;
    category: string;
    views: number;
}
*/

export interface EmailAnalyticsInterface {
    opens: number;
    clicks: number;
    deliveries: number;
    chartData: ChartDataInterface[];
    bulkSendData: {
        bulkSends: BulkSendInterface[],
        videoPlayStats: VideoPlayStats[],
    };
}