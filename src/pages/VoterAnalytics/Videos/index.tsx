import * as interfaces from 'interfaces';
import PageLayout from 'shared/PageLayout';
import { motion } from "framer-motion";

import classes from './Videos.module.scss';
import sharedClasses from '../Shared.module.scss';
import Filter from 'components/VoterAnalytics/Filter';
import AnalyticsButton from 'components/VoterAnalytics/Buttons';
import PrimaryMetrics from 'components/VoterAnalytics/PrimaryMetrics';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import PortalPopup from "components/PortalPopup";
import VideoDetails from "components/VoterAnalytics/VideoDetails";
import { AuthService, StatService } from 'services';
import FilterBar from 'components/VoterAnalytics/FilterBar';
import { useFilterStore } from 'hooks/zustand/filterStore';
import { Chart } from 'components/VoterAnalytics/Chart';
import { useAnalytics, useVideos } from 'hooks/reactQuery/useAnalyticsQueries';
import { Popover, PopoverContent, PopoverTrigger } from 'shared/ui/popover';
import ExportButton from 'components/VoterAnalytics/ExportElementBtn';
import { cn, numberWithCommas } from 'utils/utils';
import moment from 'moment';
import { Tooltip } from '@material-ui/core';
import Loader from 'components/Loader';
import { Button } from "shared/ui/button"
import xlsx from 'json-as-xlsx';
import { useUIStore } from 'hooks/zustand/uiStore';
import { useStatsStore } from 'hooks/zustand/statsStore';
import VideoStatPreviewComponent from 'components/VoterAnalytics/VideoStatPreviewComponent';
import useExportPdf from 'hooks/useExportPdf.hook';
import emptyThumbnailUrl from 'components/VideoHelpers/emptyThumbnailUrl';
import { AnswerVideoStatusEnum } from 'components/AnswerVideoStatus/useAnswerVideoStatus';
import { useServiceContext } from 'services/ServiceProvider';

export default function Videos(props: { client: interfaces.ClientInterface; service: StatService; authService: AuthService; user?: interfaces.UserInterface; }) {
  const { client, service, authService, user } = props;
  const { adminStatsService } = useServiceContext();
  const [analytics, setAnalytics] = useState<any>([]);
  const filterStore = useFilterStore();
  const [videos, setVideos] = useState<interfaces.VideoStatInterface[]>([]);
  const [videoFeedbackStats, setVideoFeedbackStats] = useState<interfaces.VideoStatInterfaceVideo['videoFeedbackStats'][]>([]);
  const [completionRateStats, setCompletionRateStats] = useState<interfaces.VideoStatInterfaceVideo['completionRateStats'][]>([]);
  const [nationalCompletionRate, setNationalCompletionRate] = useState<number>(0);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 8 }) // Set initial pagination state
  const uiStore = useUIStore();
  const { uiType, setUiType } = uiStore;
  const { exportPDF } = useExportPdf(setUiType);

  const chartRef = useRef(null);

  const videoViews = useMemo(() =>
    (service.analytics || []).filter((item: any) => ['setVideo', 'playVideo', 'pauseVideo'].includes(item.event_action)),
    [service.analytics]
  );

  const chartData = useMemo(() => {
    if (videoViews.length === 0) return [];

    const pageViewsSortedByDate = videoViews.sort((a: any, b: any) => {
      return moment(a.created_at).diff(moment(b.created_at));
    });
    const firstEventDate = moment(pageViewsSortedByDate[pageViewsSortedByDate.length - 1].created_at);
    const lastEventDate = moment(pageViewsSortedByDate[0].created_at);
    const isYearOrMore = lastEventDate.diff(firstEventDate, 'months') <= -6;
    const format = isYearOrMore ? 'YYYY-MM' : 'YYYY-MM-DD';

    const groupedData = videoViews.reduce((acc: any, event: any) => {
      const date = moment(event.created_at).format(format);

      if (!acc[date]) acc[date] = 0;
      acc[date]++;

      return acc;
    }, {});

    return Object.keys(groupedData).map(date => ({
      date,
      data: groupedData[date]
    }));
  }, [videoViews]);

  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      pageSize: uiType === "pdf" ? 4 : 8,
    }));
  }, [uiType]);

  // Fetch analytics data
  const analyticsQuery = useAnalytics(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin",
    client?.id
  );

  // Fetch videos data
  const videosQuery = useVideos(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin",
    client?.id
  );

  // Check if analytics data is empty and refetch if needed
  useEffect(() => {
    const hasEmptyAnalytics = !service.analytics || service.analytics.length === 0;
    const hasEmptyVideos = !service.videos || service.videos.length === 0;

    if (hasEmptyAnalytics || hasEmptyVideos) {
      // Refetch analytics and videos data when page opens with empty data
      if (hasEmptyAnalytics) {
        analyticsQuery.refetch();
      }
      if (hasEmptyVideos) {
        videosQuery.refetch();
      }
    }
  }, []); // Empty dependency array to run only on mount

  // Set loading state based on queries
  const loading = analyticsQuery.isLoading || videosQuery.isLoading;

  // Update state when analytics data is available and update service properties
  useEffect(() => {
    if (analyticsQuery.data) {
      // Update service properties with the analytics data
      service.analytics = analyticsQuery.data.analytics;
      service.mobileDesktop = analyticsQuery.data.mobileDesktop;
      service.referralPercentage = analyticsQuery.data.referralPercentage;
      service.engagementsChart = analyticsQuery.data.engagementsChart;
      service.sentimentAnalytics = analyticsQuery.data.sentiments;
      service.aiQueriesChart = analyticsQuery.data.aiQueriesChart;
      service.aiQueriesSentiments = analyticsQuery.data.aiQueriesSentiments;

      // Recompute values to update service.computedValues
      service.computeValues();

      // Update local state
      setAnalytics(analyticsQuery.data.analytics);
    }
  }, [analyticsQuery.data, service]);

  // Update state when videos data is available and update service properties
  useMemo(() => {
    if (videosQuery.data) {
      // Update service properties with the videos data
      service.videos = videosQuery.data.videos;
      service.videoPlayStats = videosQuery.data.videoPlayStats || [];

      // Recompute quartile data to update service.computedValues
      service.computeQuartileData();

      // Update local state
      setVideos(videosQuery.data.videos);
      setVideoFeedbackStats(videosQuery.data.videoFeedbackStats);
      setCompletionRateStats(videosQuery.data.completionRateStats);
      setNationalCompletionRate(videosQuery.data.nationalCompletionRate?.[0]?.average_completion_rate);
    }
  }, [videosQuery.data, service, filterStore.timeFilter]);

  const [isVideoDetailsOpen, setVideoDetailsOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<interfaces.VideoStatInterface | null>(null);

  const openVideoDetails = (video: interfaces.VideoStatInterface) => {
    video.videoFeedbackStats = videoFeedbackStats.find((vfs: any) => vfs.id?.toString() === video.id.toString());
    video.completionRateStats = completionRateStats.find((crs: any) => crs.id?.toString() === video.id.toString());
    video.nationalCompletionRate = nationalCompletionRate;

    setSelectedVideo(video);
    setVideoDetailsOpen(true);

    adminStatsService?.trackEvent('VideosPage', 'open_video_details');
  }

  const closeVideoDetails = () => {
    setVideoDetailsOpen(false);

    if (selectedVideo) {
      adminStatsService?.trackEvent('VideosPage', 'close_video_details');
    }

    setSelectedVideo(null);
  }

  const videosCsv = useMemo(() => {
    return service.getVideosCsv();
  }, [service, analytics]);

  const paginatedVideos = useMemo(() => {
    const combinedVideos = videos.reduce<interfaces.VideoStatInterface[]>((acc, video) => {
      const existingVideo = acc.find(v => v.id === video.id);
      if (existingVideo) {
        existingVideo.views = parseInt(existingVideo.views.toString() || '0') + parseInt(video.views.toString() || '0');
      } else {
        acc.push({ ...video });
      }
      return acc;
    }, []);

    const start = pagination.pageIndex * pagination.pageSize;
    const end = start + pagination.pageSize;
    const slicedVideos = combinedVideos.sort((a: any, b: any) => a.views - b.views).reverse().slice(start, end);

    return slicedVideos;
  }, [videos, pagination.pageIndex, pagination.pageSize]);

  const topVideo = paginatedVideos.reduce((prev, current) =>
    Number(prev.views) > Number(current.views) ? prev : current, videos[0]);

  if (topVideo) {
    topVideo.videoFeedbackStats = videoFeedbackStats.find((vfs: any) => vfs.id?.toString() === topVideo.id.toString());
    topVideo.completionRateStats = completionRateStats.find((crs: any) => crs.id?.toString() === topVideo.id.toString());
    topVideo.nationalCompletionRate = nationalCompletionRate;
  }

  const xlsxSettings = {
    fileName: 'videos'
  }

  const downloadExcelHandler = () => {
    const data = service.getVideosXlsx();
    xlsx(data, xlsxSettings);
    adminStatsService?.trackEvent('VideosPage', 'export_excel');
  }

  // Update handler to forward parameters
  const exportPDFHandler = (exportEngagements: boolean, exportVideos: boolean, exportEmails: boolean) => {
    exportPDF(exportEngagements, exportVideos, exportEmails, loading);
    adminStatsService?.trackEvent('VideosPage', 'export_pdf');
  };

  function isAfterMay2024(answer: interfaces.VideoStatInterface) {
    const inputDate = new Date(answer.createdAt);
    const comparisonDate = new Date("2024-05-20T09:00:00Z");

    return inputDate > comparisonDate;
  }

  const getStatusFromAnswer = useCallback((video) => {
    const isProcessing =
      isAfterMay2024(video) && video.mp4VideoStatus === "";
    const hasSelectedThumbnail = video.imageUrl !== emptyThumbnailUrl;
    return isProcessing
      ? AnswerVideoStatusEnum.processing
      : hasSelectedThumbnail
        ? AnswerVideoStatusEnum.published
        : AnswerVideoStatusEnum.unpublished;
  }, [videos]);

  return (
    <>
      <PageLayout user={user} client={client} authService={authService} className={classes.wrapper}>
        {loading ? (
          <Loader customId='loader' />
        ) : (
          <main className={classes.mainContainer}>
            <section className={classes.contentWrapper}>
              <div className={classes.filterContainer}>
                <div className={classes.filterHeader}>
                  <div className={uiType !== "pdf" ? "flex-1" : ""}>
                    {uiType === "pdf" && <h2 className={"w-full"}>{client.name} Report</h2>}
                    {uiType === "pdf" && <h3 className={"text-2xl mb-5"}>Date: {filterStore.timeFilter?.name || 'Default - All Time'}</h3>}
                    <h1 className={classes.pageTitle}>Videos</h1>
                  </div>
                  {
                    uiType !== "pdf" && <>
                      <Filter isSuperAdmin={user?.accessLevel === "super admin"} type="Default" />
                      <AnalyticsButton
                        onClickPDF={exportPDFHandler}
                        onClickExcel={downloadExcelHandler}
                        csvData={videosCsv || ""}
                        csvFilename="videos.csv"
                        isSuperAdmin={user?.accessLevel === "super admin"}
                      />
                    </>
                  }
                </div>

                {uiType !== "pdf" && <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.1 }}
                  className={cn(classes.filterBarContainer, classes.hideOnMobile)}>
                  <FilterBar />
                </motion.div>}

                <div className={sharedClasses.metricContainer}>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.15 }}
                    className={sharedClasses.statItemContainer}>
                    <img
                      className={sharedClasses.statIcon}
                      loading="lazy"
                      alt=""
                      src="/analytics/icons/computer-icon.svg"
                    />
                    <div className={sharedClasses.metricLabelsContainer}>
                      <div className={sharedClasses.totalTraffic}>Total Video Views</div>
                      <b className={sharedClasses.metricDurations}>{service.computedValues.totalVideoViews.toLocaleString()}</b>
                    </div>
                  </motion.div>
                  <img
                    className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                    alt=""
                    src="/analytics/chart/vertical-divider.svg"
                  />
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className={sharedClasses.statItemContainer}>
                    <img
                      className={sharedClasses.statIcon}
                      loading="lazy"
                      alt=""
                      src="/analytics/svg-icons/timer-2.svg"
                    />
                    <div className={sharedClasses.metricLabelsContainer}>
                      <div className={sharedClasses.engagementRate}>Average Watch Time</div>
                      <b className={sharedClasses.metricValue}>
                        <span>{service.computedValues.averageWatchTime}</span>
                      </b>
                    </div>
                  </motion.div>
                  <img
                    className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                    alt=""
                    src="/analytics/chart/vertical-divider.svg"
                  />
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.45 }}
                    className={sharedClasses.statItemContainer}>
                    <img
                      className={sharedClasses.statIcon}
                      loading="lazy"
                      alt=""
                      src="/analytics/svg-icons/timer.svg"
                    />
                    <div className={sharedClasses.timeSavedContainer}>
                      <Tooltip title={"Calculated based on plays and pauses by visitors on videos on the frontend."} interactive>
                        <div className={sharedClasses.watchRateLabelContainer}>
                          <div className={sharedClasses.engagementRate}>
                            Completed Watch Rate
                          </div>
                          <img
                            className={sharedClasses.infoIcon}
                            alt=""
                            src="/analytics/svg-icons/info.svg"
                          />
                        </div>
                      </Tooltip>
                      <b className={sharedClasses.metricValue}>
                        <span>{service.computedValues.completedWatchRate}</span>
                        <span className={sharedClasses.metricUnit}>%</span>
                      </b>
                    </div>
                  </motion.div>
                </div>

                <Popover>
                  <motion.div
                    initial={{ opacity: 0, x: 40 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: 0.4 }}
                    className={cn(classes.chartContainer, 'chartContainer')}>
                    <div className={classes.chartHeaderContainer}>
                      <div className={classes.chartTitleContainer}>
                        <div className={classes.chartTitle}>Video Views</div>
                      </div>

                      <PopoverTrigger asChild>
                        <img
                          className={cn(classes.moreInfoIcon, "cursor-pointer")}
                          loading="lazy"
                          alt=""
                          src="/analytics/icons/more-info-icon.png"
                        />
                      </PopoverTrigger>

                      <PopoverContent className='w-fit'>
                        <ExportButton mainContent={chartRef} />
                      </PopoverContent>
                    </div>

                    <Chart type="videos" color="#F7863A" chartData={chartData} chartRef={chartRef} />
                  </motion.div>
                </Popover>
                <motion.div
                  initial={{ opacity: 0, x: 40 }}
                  animate={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.6 }}
                  className={classes.videoListContainer}>

                  <div className={classes.videoListHeaderContainer}>
                    <div className={classes.chartTitleContainer}>
                      <div className={classes.sectionTitle}>Videos</div>
                    </div>
                  </div>

                  <div className={cn(classes.videoGrid)}>
                    {paginatedVideos.map((video, i) => (
                      <div
                        key={video.id + '#' + i}
                        className={classes.videoCard}
                        onClick={() => openVideoDetails(video)}
                      >
                        <img
                          className={classes.videoThumbnail}
                          loading="lazy"
                          alt={video.title}
                          src={video.thumbnailUrl}
                        />

                        <div className={classes.videoTitle}>
                          {video.title}
                        </div>

                        <div className={classes.videoMeta}>
                          <p className={classes.viewsText}>{video.views} Views • {moment(video.createdAt).format('DD MMM')}</p>
                          <span className={classes.categoryBadge}>{video.category}</span>
                          <span className={`${classes.categoryBadge} ${classes.accentBadge}`}>{getStatusFromAnswer(video)}</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {uiType === "pdf" && <VideoStatPreviewComponent data={topVideo} className="w-[770px] mx-auto pt-5" />}

                  {(new Set((videos.map(v => v.id)))).size > 8 && uiType !== "pdf" && ( // Hide pagination controls if 8 or fewer videos
                    <div className="w-[100%] flex justify-center gap-2 items-center mt-4 text-sm">
                      <Button
                        onClick={() => {
                          setPagination((prev) => ({ ...prev, pageIndex: prev.pageIndex - 1 }));
                          adminStatsService?.trackEvent('VideosPage', 'pagination');
                        }}
                        disabled={pagination.pageIndex === 0}
                      >
                        &lt;
                      </Button>
                      <span>
                        {pagination.pageIndex + 1} of {Math.ceil(videos.length / pagination.pageSize)}
                      </span>
                      <Button
                        onClick={() => {
                          setPagination((prev) => ({ ...prev, pageIndex: prev.pageIndex + 1 }));
                          adminStatsService?.trackEvent('VideosPage', 'pagination');
                        }}
                        disabled={pagination.pageIndex >= Math.ceil(videos.length / pagination.pageSize) - 1}
                      >
                        &gt;
                      </Button>
                    </div>
                  )}
                </motion.div>
              </div>
            </section>
          </main>
        )}
      </PageLayout>
      {isVideoDetailsOpen && selectedVideo && (
        <PortalPopup
          overlayColor="rgba(113, 113, 113, 0.3)"
          placement="Centered"
          onOutsideClick={closeVideoDetails}
        >
          <VideoDetails onClose={closeVideoDetails} video={selectedVideo} />
        </PortalPopup>
      )}
    </>
  )
}
