@import 'styles/variables';

.editUser {
  width: 100%;
  text-align: center;
  font-size: 16px;

  .title {
    padding-bottom: 20px;
    margin-bottom: 10px;
    font-weight: 500;
  }

  .subTitle {
    font-size: 18px;
    color: $dark-blue;
    font-weight: 400;
  }

  .bodySection {
    padding: 20px;

    textarea {
      width: 100%;
      height: 322px;
      max-height: 500px;
      border: 1px solid $offwhite;
      padding: 10px;
      font-size: 16px;
      border-radius: 5px;
      white-space: pre-line;
      font-weight: 500;
      color: black;
      font-family: monospace;
      outline: $grey 2px dashed;

      &:focus {
        outline: $blue 2px dashed !important;
      }

      &::placeholder {
        color: $grey;
        font-weight: 500;
      }
    }

  }

  .buttonWrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      color: $blue;
      padding: 7px 35px;

      &.primary {
        color: white;
      }

      &:first-child {
        margin-right: 20px;
      }

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}

label {
  display: block;
  text-align: left;
}

.textLeft {
  display: block;
  text-align: left;
}

.multiSelect {
  width: auto !important;
  position: relative;
}

.multiSelectTrigger {
  width: auto !important;
  padding: 12px 16px;
  border: 1px solid $grey;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  transition: border-color 0.2s ease;
  margin-bottom: 20px;

  &:hover {
    border-color: $dark-blue;
  }

  span {
    color: #333;
    text-align: left;
  }
}

.chevron {
  transition: transform 0.2s ease;
  color: #666;
  width: auto !important;

  &.open {
    transform: rotate(180deg);
  }
}

.multiSelectDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid $grey;
  border-top: none;
  border-radius: 0 0 6px 6px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.multiSelectOption {
  padding: 12px 16px;
  display: flex;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &.selected {
    background-color: #e3f2fd;
  }

  span {
    width: auto !important;
    margin-left: 8px;
    font-size: 16px;
  }
}

.checkbox {
  margin-bottom: 0 !important;
  width: auto !important;
  cursor: pointer;
}