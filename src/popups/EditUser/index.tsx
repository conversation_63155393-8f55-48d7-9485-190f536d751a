import { SyntheticEvent, useEffect, useState } from 'react';
import { Field, Form, FormikProvider, useFormik, useFormikContext } from "formik";
import * as Yup from "yup";
import { toast, ToastContainer } from 'react-toastify';
import cn from 'classnames';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import { UserService } from 'services';
import { UserInterface, ClientInterface } from 'interfaces';
import { useServiceContext } from 'services/ServiceProvider';

import classes from './EditUser.module.scss';
import FormClasses from 'shared/Forms/Forms.module.scss';

interface EditUserProps {
  authUser?: UserInterface;
  user: UserInterface;
  userService?: UserService;
  records: UserInterface[];
  setRecords: any;
  setNewUserRecords: any;
  handleClose: () => void;
}

interface MultiSelectProps {
  options: { id: string; name: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder: string;
}

const MultiSelect: React.FC<MultiSelectProps> = ({ options, selected, onChange, placeholder }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (optionId: string) => {
    const newSelected = selected.includes(optionId)
      ? selected.filter(item => item !== optionId)
      : [...selected, optionId];
    onChange(newSelected);
  };

  const getDisplayValue = () => {
    if (selected.length === 0) return placeholder;
    if (selected.length === 1) {
      const option = options.find(opt => opt.id === selected[0]);
      return option ? option.name : selected[0];
    }
    return `${selected.length} selected`;
  };

  return (
    <div className={classes.multiSelect}>
      <div
        className={classes.multiSelectTrigger}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{getDisplayValue()}</span>
        <FontAwesomeIcon icon={['fas', 'chevron-down']} className={cn(classes.chevron, isOpen && classes.open)} />
      </div>
      {isOpen && (
        <div className={classes.multiSelectDropdown}>
          {options.map(option => (
            <div
              key={option.id}
              className={cn(classes.multiSelectOption, selected.includes(option.id) && classes.selected)}
              onClick={() => toggleOption(option.id)}
            >
              <span>{option.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default function EditUser({
  authUser,
  user,
  userService,
  records,
  setRecords,
  setNewUserRecords,
  handleClose,
}: EditUserProps) {
  const [updatedUser, setUser] = useState(user);
  const [isUpdating, setUpdated] = useState(false);
  const { adminStatsService, clientService } = useServiceContext();

  const [save, setSave] = useState(false);
  const [saveCounter, setCounter] = useState(0);
  const [clients, setClients] = useState<ClientInterface[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);

  // Fetch all clients when component mounts
  useEffect(() => {
    if (authUser?.accessLevel === 'super admin' && clientService) {
      clientService.getAllClients((fetchedClients: ClientInterface[]) => {
        setClients(fetchedClients);
      });
    }
  }, [clientService, authUser?.accessLevel]);

  // Initialize selected clients when user changes
  useEffect(() => {
    // Extract client IDs from the user's clients array
    const userWithClients = user as UserInterface & { clients?: ClientInterface[] };
    if (userWithClients.clients && Array.isArray(userWithClients.clients)) {
      const clientIds = userWithClients.clients.map((client: ClientInterface) => client.id);
      setSelectedClients(clientIds);
    } else {
      setSelectedClients([]);
    }
  }, [user]);

  const updateField = (u: Partial<UserInterface>) => {
    const updatedUserChanges = { ...updatedUser, ...u }
    setUser(updatedUserChanges);
  }

  const updateRecord = async (id: string, u: Partial<UserInterface>, callback: Function | null = null) => {
    const updatedUserChanges = {
      ...updatedUser,
      ...u,
      id,
      ...(!u.clientId && { clientId: authUser?.clientId, client: authUser?.client }),
      // Include clients array for super admin users
      ...(authUser?.accessLevel === 'super admin' && selectedClients.length > 0 && { clients: selectedClients })
    };

    if (isUpdating) return;
    setUpdated(true);

    const processRequest = (item: UserInterface, error: any = null) => {
      setUpdated(false);

      if (!item?.id) {
        toast.error(
          error.message || "Saving the user failed. Please try again later.",
        );
      } else {
        setUser(updatedUserChanges);
        setRecords(records.map((u2: UserInterface) => u2.id === updatedUser.id ? { ...item, client: user.client } : u2));
        if (!id) setNewUserRecords([...records, { ...item, client: user.client || authUser?.client }]);
        toast.success("User saved successfully.");
        !!callback && setTimeout(() => callback(), 2000);
      }
    }

    if (!updatedUser.id)
      return userService?.createUser(updatedUserChanges, processRequest);
    else
      return userService?.updateUser(updatedUserChanges, processRequest);
  }

  const handleSave = async (user: UserInterface) => {
    adminStatsService?.trackEvent('Team', updatedUser.id ? 'save_user_edit' : 'save_new_user');
    await updateRecord(updatedUser.id, updatedUser, handleClose);

    setSave(!save);
  }

  const errorMessages = {
    firstName: 'Please enter your first name',
    lastName: 'Please enter your last name',
    email: 'Please enter a valid email address',
    zip: 'Please enter a valid zip',
    pass: 'Please enter a valid password'
  }

  const profileForm = useFormik({
    initialValues: {
      firstName: updatedUser?.firstName || '',
      lastName: updatedUser?.lastName || '',
      email: updatedUser?.email || '',
    },
    validationSchema: Yup.object().shape({
      firstName: Yup.string().required(errorMessages.firstName),
      lastName: Yup.string().required(errorMessages.lastName),
      email: Yup.string().required(errorMessages.email).email(errorMessages.email),
    }),
    onSubmit: (values, actions) => { }
  });

  const passwordForm = useFormik({
    initialValues: { pass: '', confirmation: '' },
    validationSchema: Yup.object().shape({
      pass: Yup.string(),
      confirmation: Yup.string(),
    }),
    onSubmit: (values, actions) => { }
  });

  const profileFocus = profileForm.touched,
    profileErrors = profileForm.errors,
    passFocus = passwordForm.touched,
    passErrors = passwordForm.errors;

  const invalid = {
    email: !updatedUser.email || !updatedUser.email.match(/[^@]+@[^.]+\.[a-z]+/),
    firstName: !updatedUser.firstName || !updatedUser.firstName.match(/^[a-z0-9 ]+/i),
    lastName: !updatedUser.lastName || !updatedUser.lastName.match(/^[a-z0-9 ]+/i),
    pass: updatedUser.password && updatedUser.password.length < 3,
    confirmation: updatedUser.password && updatedUser.password !== updatedUser.passwordConfirmation
  };

  const errors = {
    email: profileErrors.email && invalid.email && profileFocus.email && FormClasses.hasError,
    firstName: profileErrors.firstName && invalid.firstName && profileFocus.firstName && FormClasses.hasError,
    lastName: profileErrors.lastName && invalid.lastName && profileFocus.lastName && FormClasses.hasError,
    pass: passErrors.pass && invalid.pass && passFocus.pass && FormClasses.hasError,
    confirmation: passErrors.confirmation && invalid.confirmation && passFocus.confirmation && FormClasses.hasError,
  }

  function SubmitOutsideForm() {
    var values: any = useFormikContext().values
    const setTouched = useFormikContext().setTouched;

    useEffect(() => {
      if (!save) return

      var invalid: boolean = false;
      var error: string | null = null;

      invalid = (!values.email || !values.firstName?.match(/^[a-z0-9 ]+/i) || !values.lastName?.match(/^[a-z0-9 ]+/i) || !values.email.match(/[^@]+@[^.]+\.[a-z]+/))
      // error = invalid ? 'Please make sure the required fields are present.' : null

      if (!values.email || !values.email.match(/[^@]+@[^.]+\.[a-z]+/)) values.email = updatedUser?.email
      if (!values.firstName) values.firstName = updatedUser?.firstName
      if (!values.lastName) values.lastName = updatedUser?.lastName

      // if (error) {
      //   // toast.error(error);
      //   setTouched({'email': true, 'firstName': true, 'lastName': true});
      // } else {
      //   updateRecord(updatedUser.id, {
      //     ...updatedUser,
      //     ...values
      //   })
      // }

      setSave(false);
    }, [values, saveCounter, save]);

    return null;
  }

  function SubmitPasswordForm() {
    var values: any = useFormikContext().values

    useEffect(() => {
      if (!save) return

      var invalid: boolean = false
      var error: string | null = null

      if (
        ![values.confirmation].includes(values.pass) ||
        (values.pass === '' && values.confirmation !== '') ||
        (values.pass !== '' && values.confirmation === '')
      ) invalid = !invalid

      if (!invalid) {
        setUser({ ...updatedUser, password: values.pass, passwordConfirmation: values.confirmation });
      }

      if (!save) return

      // error = invalid ? 'Please enter both passwords so they match.' : null

      if (!invalid && values.pass && values.confirmation) {
        updateRecord(updatedUser.id, {
          password: values.pass,
          passwordConfirmation: values.confirmation
        });

        setTimeout(() => passwordForm.resetForm(), 500)
      }

      // if (error) toast.error(error)

      setCounter(0)
      setSave(false);
    }, [values, saveCounter, save]);

    return null;
  }

  // console.log('user', updatedUser.receiveAnswerNotifications, updatedUser.receiveQuestionNotifications);

  return (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.editUser}>
        <div className={classes.bodySection}>
          <div className={FormClasses.Form}>
            <h1 className={FormClasses.title}>
              {updatedUser.id ?
                <>Edit <b>{updatedUser.firstName} {updatedUser.lastName}</b></> :
                'Create A New User'
              }
              {authUser?.accessLevel === 'super admin' && <>
                <br />
                <small>{updatedUser.client?.name}</small>
              </>}
            </h1>
            <FormikProvider key="updateFormProvider" value={profileForm}>
              <SubmitOutsideForm key="updateFormButtonHandler" />

              <Form key="updateForm" className={FormClasses.Form}>
                <div className={FormClasses.SideBySide}>
                  <div className={FormClasses.InputWithLabel}>
                    <label>First Name</label>
                    <Field
                      type="text"
                      name="firstName"
                      className={errors.firstName}
                      value={updatedUser.firstName}
                      onChange={(e: SyntheticEvent) => { updateField({ firstName: (e.target as HTMLInputElement).value }) }}
                    />
                    {errors.firstName && <div className={FormClasses.errorText}>{errorMessages.firstName}</div>}
                  </div>
                  <div className={FormClasses.InputWithLabel}>
                    <label>Last Name</label>
                    <Field
                      type="text"
                      name="lastName"
                      className={errors.lastName}
                      value={updatedUser.lastName}
                      onChange={(e: SyntheticEvent) => { updateField({ lastName: (e.target as HTMLInputElement).value }) }}
                    />
                    {errors.lastName && <div className={FormClasses.errorText}>{errorMessages.lastName}</div>}
                  </div>
                </div>
                <div className={FormClasses.InputWithLabel}>
                  <label>Email</label>
                  <Field
                    type="email"
                    name="email"
                    className={errors.email}
                    value={updatedUser.email}
                    onChange={(e: SyntheticEvent) => { updateField({ email: (e.target as HTMLInputElement).value }) }}
                    placeholder="Enter your email"
                  />
                  {errors.email && <div className={FormClasses.errorText}>{errorMessages.email}</div>}
                </div>
                <div className={FormClasses.InputWithLabel}>
                  <label>Role</label>
                  <select name="accessLevel" placeholder="Select a Role" onChange={(e) => updateField({ accessLevel: e.target.value })}>
                    {authUser?.accessLevel === 'super admin' && <option value="super admin" selected={updatedUser.accessLevel === 'super admin'}>Super Admin</option>}
                    {['super admin', 'admin'].includes(authUser?.accessLevel || '') && updatedUser.clientId && <option value="admin" selected={updatedUser.accessLevel === 'admin'}>Admin</option>}
                    {updatedUser.clientId && <option value="manager" selected={updatedUser.accessLevel === 'manager'}>Manager</option>}
                    {/* {authUser?.accessLevel === 'super admin' && <option value="user" selected={updatedUser.accessLevel === 'user'}>User</option>}
                    {authUser?.accessLevel === 'super admin' && <option value="visitor" selected={updatedUser.accessLevel === 'visitor'}>Visitor</option>} */}
                  </select>
                </div>

                {authUser?.accessLevel === 'super admin' && (
                  <div className={FormClasses.InputWithLabel}>
                    <label>Clients</label>
                    <MultiSelect
                      options={clients.map(client => ({ id: client.id, name: client.name }))}
                      selected={selectedClients}
                      onChange={setSelectedClients}
                      placeholder="Select Clients"
                    />
                  </div>
                )}

                <div className={FormClasses.checkbox}>
                  <input
                    type="checkbox"
                    name="canReceiveQuestionEmails"
                    onChange={() => updateField({ receiveQuestionNotifications: !updatedUser.receiveQuestionNotifications })}
                    checked={updatedUser.receiveQuestionNotifications === true}
                  />
                  Receive Question Notifications
                </div>

                <div className={FormClasses.checkbox}>
                  <input
                    type="checkbox"
                    name="canReceiveAnswerEmails"
                    onChange={() => updateField({ receiveAnswerNotifications: !updatedUser.receiveAnswerNotifications })}
                    checked={updatedUser.receiveAnswerNotifications === true}
                  />
                  Receive Answer Notifications
                </div>
              </Form>
            </FormikProvider>

            <h1 className={FormClasses.title}>Update Password</h1>
            <FormikProvider key="passFormProvider" value={passwordForm}>
              <SubmitPasswordForm key="passFormButtonHandler" />

              <Form key="passForm" className={FormClasses.Form}>
                <div className={FormClasses.SideBySide}>
                  <div className={FormClasses.InputWithLabel}>
                    <label>Password</label>
                    <Field
                      type="password"
                      name="pass"
                      className={errors.pass}
                      value={updatedUser.password}
                      onChange={(e: SyntheticEvent) => { updateField({ password: (e.target as HTMLInputElement).value }) }}
                    />
                    {errors.pass && <div className={FormClasses.errorText}>{errorMessages.pass}</div>}
                  </div>
                  <div className={FormClasses.InputWithLabel}>
                    <label>Confirmation</label>
                    <Field
                      type="password"
                      name="confirmation"
                      className={errors.confirmation}
                      value={updatedUser.passwordConfirmation}
                      onChange={(e: SyntheticEvent) => { updateField({ passwordConfirmation: (e.target as HTMLInputElement).value }) }}
                    />
                  </div>
                </div>
              </Form>
            </FormikProvider>

            {['super admin', 'admin'].includes(authUser?.accessLevel || '') && (
              <>
                <div className={FormClasses.checkbox}>
                  <input
                    type="checkbox"
                    name="isMfaRequired"
                    onChange={() => updateField({ isMfaRequired: !updatedUser.isMfaRequired })}
                    checked={updatedUser.isMfaRequired === true}
                  />
                  Two-Factor Authentication Enabled
                </div>
                <small className={classes.textLeft}>
                  Login link will be sent to your email after entering the password.
                </small>
              </>
            )}

          </div>
        </div>
        <div className={classes.buttonWrapper}>
          <Button text="Cancel" customClass={classes.button} callback={() => {
            adminStatsService?.trackEvent('Team', 'cancel_user_edit');
            handleClose();
          }} />
          <Button text={isUpdating ? 'Saving...' : 'Save'} customClass={classes.button} callback={handleSave} />
        </div>
      </div>
      <ToastContainer position="top-right" />
    </Modal>
  );
}