import { ApiService } from './api.service';
import { AyrshareProfileInterface } from '../interfaces/client.interfaces';

interface AyrshareProfileResponse {
    ayrshareProfile: AyrshareProfileInterface;
    message: string;
    totalEntries: number;
}

export class AyrshareProfileService extends ApiService {
    id: string;
    ayrshareProfile: AyrshareProfileInterface | null = null;

    constructor(token?: string, clientId?: string, initialProfile?: AyrshareProfileInterface | null) {
        super(token, clientId);
        this.id = 'AyrshareProfileService';
        this.ayrshareProfile = initialProfile || null;
    }

    /**
     * Get ayrshare profile for a specific client
     * GET /api/v1.0.0/social-media/ayrshare-profile/:clientId
     */
    async getAyrshareProfile(clientId: string, callback?: (result: AyrshareProfileInterface | null, error?: any) => void): Promise<AyrshareProfileInterface | null> {
        const endpoint = `${this.base}/social-media/ayrshare-profile/${clientId}`;
        const errorIdentifier = `${this.id} -> Get Ayrshare Profile`;

        try {
            const response = await this.axiosInstance.get(endpoint);
            const responseData: AyrshareProfileResponse = response.data;

            if (!responseData || !responseData.ayrshareProfile || response.data.errors) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, response.data.errors);
                return null;
            }

            // Update the local ayrshareProfile data
            this.ayrshareProfile = responseData.ayrshareProfile;

            if (callback) callback(responseData.ayrshareProfile);
            return responseData.ayrshareProfile;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Refresh the current ayrshare profile data
     */
    async refreshProfile(callback?: (result: AyrshareProfileInterface | null, error?: any) => void): Promise<AyrshareProfileInterface | null> {
        if (!this.clientId) {
            const error = 'No client ID available for profile refresh';
            console.error(error);
            if (callback) callback(null, error);
            return null;
        }

        return this.getAyrshareProfile(this.clientId, callback);
    }

    /**
     * Get the current ayrshare profile data (from memory)
     */
    getCurrentProfile(): AyrshareProfileInterface | null {
        return this.ayrshareProfile;
    }

    /**
     * Update the local ayrshare profile data
     */
    updateProfile(profile: AyrshareProfileInterface | null): void {
        this.ayrshareProfile = profile;
    }

    /**
     * Check if the profile has connected social media accounts
     */
    hasConnectedAccounts(): boolean {
        return (this.ayrshareProfile?.socialMediaAccounts?.length ?? 0) > 0;
    }

    /**
     * Get the list of connected social media platforms
     */
    getConnectedPlatforms(): string[] {
        if (!this.ayrshareProfile?.socialMediaAccounts) {
            return [];
        }
        return this.ayrshareProfile.socialMediaAccounts.map(account => account.platform);
    }

    /**
     * Get the profile key for API calls
     */
    getProfileKey(): string | null {
        return this.ayrshareProfile?.profileKey || null;
    }

    /**
     * Get the ref ID for API calls
     */
    getRefId(): string | null {
        return this.ayrshareProfile?.refId || null;
    }

    /**
     * Clear the local profile data
     */
    clearProfile(): void {
        this.ayrshareProfile = null;
    }
}
