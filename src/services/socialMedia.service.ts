import { ApiService } from './api.service';

export interface SocialMediaAccountData {
    clientId: string;
    platform: string;
    ayrshareProfileId: number;
}

export interface AyrshareProfileData {
    title: string;
    clientId: string;
}

export interface ConnectUrlRequest {
    profileKey: string;
}

export interface ConnectUrlResponse {
    status: string;
    title: string;
    token: string;
    url: string;
    emailSent: boolean;
    expiresIn: string;
}

export interface GetProfileRequest {
    refId: string;
}

export interface SocialMediaPostRequest {
    post: string;
    platforms: string[];
    answerId: number;
    profileKey: string;
}

export interface GeneratePostRequest {
    question: string;
    answer: string;
    cityName: string;
    platform: string;
}

export interface GeneratePostResponse {
    message: string;
    postText: string;
}

export interface SocialMediaAccountResponse {
    data: any;
    ayrshareProfile: any;
}

export interface AyrshareProfileResponse {
    data: any;
    ayrshareProfile: any;
}

export class SocialMediaService extends ApiService {
    id: string;
    ayrshareProfileService?: any; // Reference to AyrshareProfileService

    constructor(token?: string, clientId?: string, ayrshareProfileService?: any) {
        super(token, clientId);
        this.id = 'SocialMediaService';
        this.ayrshareProfileService = ayrshareProfileService;
    }

    /**
     * Set the AyrshareProfileService reference for updating profile data
     */
    setAyrshareProfileService(ayrshareProfileService: any): void {
        this.ayrshareProfileService = ayrshareProfileService;
    }

    /**
     * Create new social media account
     * POST /api/v1.0.0/social-media/accounts
     */
    async createSocialMediaAccount(data: SocialMediaAccountData, callback?: (result: any, error?: any) => void) {
        const endpoint = `${this.base}/social-media/accounts`;
        const errorIdentifier = `${this.id} -> Create Social Media Account`;

        try {
            const response = await this.axiosInstance.post(endpoint, data);
            const responseData: SocialMediaAccountResponse = response.data;

            if (!responseData || !responseData.data || response.data.errors) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, response.data.errors);
                return null;
            }

            // Update the AyrshareProfileService with the new profile data
            if (this.ayrshareProfileService && responseData.ayrshareProfile) {
                console.log('Updating ayrshare profile after creating social media account:', responseData.ayrshareProfile);
                this.ayrshareProfileService.updateProfile(responseData.ayrshareProfile);
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Delete (disable) social media account
     * DELETE /api/v1.0.0/social-media/accounts/:id
     */
    async deleteSocialMediaAccount(accountId: string, callback?: (result: any, error?: any) => void) {
        const endpoint = `${this.base}/social-media/accounts/${accountId}`;
        const errorIdentifier = `${this.id} -> Delete Social Media Account`;

        try {
            const response = await this.axiosInstance.delete(endpoint);
            const responseData = response.data.data[0];

            if (!responseData || response.data.errors) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, response.data.errors);
                return null;
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Create new Ayrshare profile
     * POST /api/v1.0.0/social-media/profiles
     */
    async createAyrshareProfile(data: AyrshareProfileData, callback?: (result: any, error?: any) => void) {
        const endpoint = `${this.base}/social-media/profiles`;
        const errorIdentifier = `${this.id} -> Create Ayrshare Profile`;

        try {
            const response = await this.axiosInstance.post(endpoint, data);
            const responseData: AyrshareProfileResponse = response.data;

            if (!responseData || !responseData.data || response.data.errors) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, response.data.errors);
                return null;
            }

            // Update the AyrshareProfileService with the new profile data
            if (this.ayrshareProfileService && responseData.ayrshareProfile) {
                console.log('Updating ayrshare profile after creating profile:', responseData.ayrshareProfile);
                this.ayrshareProfileService.updateProfile(responseData.ayrshareProfile);
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Get social media profiles
     * GET /api/v1.0.0/social-media/profiles
     */
    async getAyrshareProfiles(callback?: (result: any, error?: any) => void) {
        const endpoint = `${this.base}/social-media/profiles`;
        const errorIdentifier = `${this.id} -> Get Ayrshare Profiles`;

        try {
            const response = await this.axiosInstance.get(endpoint);
            const responseData = response.data.data;

            if (!responseData || response.data.errors) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, response.data.errors);
                return null;
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Generate connection URL for social media accounts
     * POST /api/v1.0.0/social-media/connect-url
     */
    async generateConnectUrl(data: ConnectUrlRequest, callback?: (result: ConnectUrlResponse | null, error?: any) => void): Promise<ConnectUrlResponse | null> {
        const endpoint = `${this.base}/social-media/connect-url`;
        const errorIdentifier = `${this.id} -> Generate Connect URL`;

        try {
            const response = await this.axiosInstance.post(endpoint, data);
            const responseData: ConnectUrlResponse = response.data;

            if (!responseData || !responseData.url) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, 'No URL in response');
                return null;
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Get Ayrshare profile information
     * GET /api/v1.0.0/social-media/profile?refId=...
     */
    async getProfile(refId: string, callback?: (result: any, error?: any) => void): Promise<any> {
        const endpoint = `${this.base}/social-media/profile?refId=${encodeURIComponent(refId)}`;
        const errorIdentifier = `${this.id} -> Get Profile`;

        try {
            const response = await this.axiosInstance.get(endpoint);
            const responseData = response.data;

            if (!responseData) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, 'No data in response');
                return null;
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Create social media post
     * POST /api/v1.0.0/social-media/post
     */
    async createPost(data: SocialMediaPostRequest, callback?: (result: any, error?: any) => void): Promise<any> {
        const endpoint = `${this.base}/social-media/post`;
        const errorIdentifier = `${this.id} -> Create Post`;

        try {
            const response = await this.axiosInstance.post(endpoint, data);
            const responseData = response.data;

            if (!responseData) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, 'No data in response');
                return null;
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }

    /**
     * Generate social media post with AI
     * POST /api/v1.0.0/social-media/generate-post
     */
    async generatePost(data: GeneratePostRequest, callback?: (result: any, error?: any) => void): Promise<GeneratePostResponse | null> {
        const endpoint = `${this.base}/social-media/generate-post`;
        const errorIdentifier = `${this.id} -> Generate Post`;

        try {
            const response = await this.axiosInstance.post(endpoint, data);
            const responseData: GeneratePostResponse = response.data;

            if (!responseData || !responseData.postText) {
                this.reportInvalidResponseError(errorIdentifier, response);
                if (callback) callback(null, 'No post text in response');
                return null;
            }

            if (callback) callback(responseData);
            return responseData;
        } catch (error: any) {
            this.reportRequestError(errorIdentifier, error);
            if (callback) callback(null, error?.response?.data || error);
            return null;
        }
    }
}
