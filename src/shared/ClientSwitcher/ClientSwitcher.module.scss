@import 'styles/variables.scss';

.clientSwitcher {
  position: relative;
  display: inline-block;
  margin-right: 15px;
}

.trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: white;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.currentClient {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow {
  width: 15px;
  height: 15px;
  transition: transform 0.2s ease;
  filter: brightness(0) invert(1); // Makes the icon white

  &.open {
    transform: rotate(180deg);
  }
}

.dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: white;
  color: #333;
  cursor: pointer;
  text-align: left;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f5f5f5;
  }

  &.current {
    background: #e3f2fd;
    color: #1976d2;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.clientInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.clientName {
  font-weight: 500;
  font-size: 14px;
}

.accessLevel {
  font-size: 12px;
  color: #666;
  text-transform: capitalize;
}

.primaryBadge {
  background: #4caf50;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  border-radius: 4px;
}

// Mobile responsiveness
@media (max-width: 640px) {
  .clientSwitcher {
    margin-right: 10px;
  }

  .trigger {
    padding: 4px 8px;
    font-size: 12px;
  }

  .currentClient {
    max-width: 100px;
  }

  .dropdown {
    min-width: 180px;
    right: -10px;
  }

  .option {
    padding: 10px 12px;
  }

  .clientName {
    font-size: 13px;
  }

  .accessLevel {
    font-size: 11px;
  }
}