import { useState, useEffect, useRef } from 'react';
import { AuthService } from 'services';
import { ClientInterface } from 'interfaces';
import { useServiceContext } from 'services/ServiceProvider';
import classes from './ClientSwitcher.module.scss';
import { cn } from 'utils/utils';
import chevronDown from 'assets/icons/chevron-down.svg';

interface ClientSwitcherProps {
  authService: AuthService;
  currentClientId?: string;
  currentClientName?: string;
  onClientSwitch?: () => void;
}

export default function ClientSwitcher({
  authService,
  currentClientId,
  currentClientName,
  onClientSwitch
}: ClientSwitcherProps) {
  const { adminStatsService, clientService } = useServiceContext();
  const [availableClients, setAvailableClients] = useState<ClientInterface[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Get available clients and current selected client ID
    authService.getAvailableClients((clients) => {
      setAvailableClients(clients);
    });

    const currentSelectedId = authService.getSelectedClientId();
    setSelectedClientId(currentSelectedId);
  }, [authService]);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleClientSwitch = (clientId: string, clientName: string) => {
    // Use selectedClientId if available, otherwise fall back to currentClientId
    const currentlySelectedId = selectedClientId || currentClientId;

    if (clientId === currentlySelectedId) {
      setIsOpen(false);
      return;
    }

    setIsLoading(true);
    adminStatsService?.trackEvent('Header', 'switch_client');

    // Set the selected client ID in localStorage
    authService.setSelectedClientId(clientId);
    setSelectedClientId(clientId);

    setIsLoading(false);
    setIsOpen(false);

    // Trigger a page reload to update all components with new client context
    if (onClientSwitch) {
      onClientSwitch();
    } else {
      window.location.reload();
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    adminStatsService?.trackEvent('Header', isOpen ? 'close_client_switcher' : 'open_client_switcher');
  };

  // Don't render if there's only one client or no clients
  if (availableClients.length <= 1) {
    return null;
  }

  return (
    <div className={classes.clientSwitcher} ref={dropdownRef}>
      <button
        className={classes.trigger}
        onClick={toggleDropdown}
        disabled={isLoading}
        aria-label="Switch client"
        aria-expanded={isOpen}
      >
        <span className={classes.currentClient}>
          {currentClientName || 'Select Client'}
        </span>
        <img
          src={chevronDown}
          alt="Dropdown arrow"
          className={cn(classes.arrow, isOpen && classes.open)}
        />
      </button>

      {isOpen && (
        <div className={classes.dropdown}>
          {availableClients.map((client) => (
            <button
              key={client.id}
              className={cn(
                classes.option,
                client.id === currentClientId && classes.current
              )}
              onClick={() => handleClientSwitch(client.id, client.name)}
              disabled={isLoading}
            >
              <div className={classes.clientInfo}>
                <span className={classes.clientName}>{client.name}</span>
              </div>
            </button>
          ))}
        </div>
      )}

      {isLoading && (
        <div className={classes.loadingOverlay}>
          Switching...
        </div>
      )}
    </div>
  );
}
