import { useLocation } from 'react-router-dom';
import classes from './Header.module.scss';
import { ClientInterface, UserInterface } from "interfaces";
import { useMemo } from 'react';
import defaultLogo from 'assets/logo/Logo-Full.png';
import { AuthService } from 'services';
import { useUIStore } from 'hooks/zustand/uiStore';
import { cn } from 'utils/utils';
import { useServiceContext } from 'services/ServiceProvider';
import ClientSwitcher from 'shared/ClientSwitcher';

interface HeaderProps {
  user?: UserInterface | null;
  client?: ClientInterface | null;
  authService?: AuthService;
  onClientSwitch?: () => void;
}

export default function Header(props: HeaderProps) {
  const { user, client, authService, onClientSwitch } = props;
  const { adminStatsService } = useServiceContext();
  const location = useLocation();
  const uiType = useUIStore(state => state.uiType);
  const { isSidebarOpen, toggleSidebar } = useUIStore(state => state);

  const pageName = useMemo(() => {
    switch (location.pathname) {
      case "/voter-analytics":
        return "Analytics"
      case "/voter-analytics/videos":
        return "Analytics"
      case "/voter-analytics/emails":
        return "Analytics"
      case "/questions-list":
        return "Questions"
      case "/questions-list/review":
        return "Questions"
      case "/questions-list/archive":
        return "Questions"
      case "/answers-list":
        return "Answers"
      case "/answers-list/drafts":
        return "Answers"
      case "/team":
        return "Team"
      case "/edit-client":
        return "Settings"
      // case "/super":
      //   return "Public Pages"
      // case "/super/new":
      //   return "Create New Page"

      default:
        break;
    }
  }, [location])

  return (
    <header className={classes.Header}>
      <div className={classes.wrapper}>
        <img className={cn(classes.logo, uiType === "pdf" && "hidden")} src={client?.logoURL && user?.accessLevel !== "super admin" ? client?.logoURL : defaultLogo} alt="Client logo" />

        {uiType === "pdf" ? <div className={classes.pdfHeader}>
          <img className={classes.logo} src={defaultLogo} alt="Repd logo" />
          <h2>+</h2>
          <img className={classes.logo} src={client?.logoURL} alt="Client logo" />
        </div> : (
          <>
            <h1 className={classes.hideOnMobile}>{pageName}</h1>
            <button
              className={classes.hamburgerButton}
              onClick={() => {
                adminStatsService?.trackEvent('Header', isSidebarOpen ? 'close_sidebar' : 'open_sidebar');
                toggleSidebar();
              }}
              aria-label="Toggle sidebar"
              aria-expanded={isSidebarOpen}
            >
              <div className={cn(classes.hamburgerIcon, isSidebarOpen && classes.open)}>
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          </>
        )}

        <div className={cn(classes.headerActions, classes.hideOnMobile, uiType === "pdf" && "hidden")}>
          {(user?.accessLevel === "admin" || user?.accessLevel === "manager") && authService && (
            <ClientSwitcher
              authService={authService}
              currentClientId={client?.id}
              currentClientName={client?.name}
              onClientSwitch={onClientSwitch}
            />
          )}
          <div
            className={classes.signOut}
            onClick={() => {
              adminStatsService?.trackEvent('Header', 'sign_out');
              authService?.logOut();
            }}
          >
            Sign out
          </div>
        </div>
      </div>
    </header>
  );
}
