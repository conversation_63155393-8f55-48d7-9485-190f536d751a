$blue: #567deb;
$bg-blue: #f6f8fa;
$dark-blue: #34538d;
$pale-blue: #eef2fc;
$menu-blue: #22365c;
$header-blue: #1e293d;
$transparent-blue: hsla(220, 65%, 20%, 0.5);

$yellow: #ffc328;
$pale-yellow: #ffffca;
$dark-yellow: #ce9400;

$red: #ff2f28;

$mint-green: #29b36e;
$pale-mint-green: #CED;
$dark-mint-green: #22925a;
$green: #22925a;

$offwhite: #f0f0f0;
$transparent-white: rgba(255, 255, 255, 0.95);
$transparent-white-low: rgba(255, 255, 255, 0.5);

$light-grey: #d0d0d0;
$grey: #d8d8d8;
$dark-grey: #999999;
$charcoal: #333333;
$transparent-black: rgba(0, 0, 0, 0.6);
$transparent-black-low: rgba(0, 0, 0, 0.1);

$background: white;

:root {
  /* fonts */
  //--font-plus-jakarta-sans: 'Plus Jakarta Sans';
  
/* Font Sizes */
--font-size-sm: 14px;
--font-size-xl: 20px;
--font-size-base: 16px;
--font-size-xs: 12px;
--font-size-8xl: 27px;
--font-size-15xl: 34px;
--font-size-35xl: 54px;
--font-size-13xl: 32px;
--font-size-24xl: 43px;
--font-size-lg: 18px;
--font-size-5xl: 24px;
--font-size-lgi: 19px;

/* Colors */
--color-whitesmoke: #f6f8fa;
--color-white: #fff;
--color-gainsboro-100: #dce5ed;
--color-gainsboro-200: #d3d8e3;
--color-slategray-100: #445472;
--color-slategray-200: rgba(68, 84, 114, 0.75);
--color-crimson: #e73067;
--color-crimson-100: #e73067;
--color-crimson-200: rgba(231, 48, 103, 0.75);
--color-black: #000;
--color-orchid: #c169d7;
--color-silver: #b8bfca;
--color-gray: #1e293d;
--color-chocolate: #f7863a;
--color-dodgerblue: #136ac6;

/* Gaps */
--gap-xl: 20px;
--gap-11xl: 30px;
--gap-10xs: 3px;
--gap-7xs: 6px;
--gap-3xs: 10px;
--gap-9xl: 28px;
--gap-603xl: 622px;
--gap-9xs: 4px;
--gap-7xl: 26px;
--gap-base: 16px;
--gap-12xs: 1px;
--gap-31xl: 50px;
--gap-6xl: 25px;
--gap-5xl: 24px;
--gap-91xl: 110px;
--gap-36xl: 55px;
--gap-mini: 15px;
--gap-lgi-7: 19.7px;
--gap-lg: 18px;
--gap-3xl: 22px;
--gap-smi: 13px;
--gap-913xl: 932px;
--gap-lgi: 19px;
--gap-2xl: 21px;
--gap-12xs-1: 0.1px;
--gap-4xs: 9px;
--gap-6xs: 7px;
--gap-2xl-9: 21.9px;
--gap-2xl-7: 21.7px;
--gap-xl-9: 20.9px;
--gap-7xs-8: 5.8px;
--gap-5xs: 8px;

/* Paddings */
--padding-xl: 20px;
--padding-11xl: 30px;
--padding-5xl: 24px;
--padding-6xl: 25px;
--padding-15xl: 34px;
--padding-3xl: 22px;
--padding-mini: 15px;
--padding-smi: 13px;
--padding-5xs-5: 7.5px;
--padding-5xs: 8px;
--padding-11xs: 2px;
--padding-sm: 14px;
--padding-lg: 18px;
--padding-71xl: 90px;
--padding-6xs: 7px;
--padding-3xs: 10px;
--padding-13xl: 32px;
--padding-8xs: 5px;
--padding-21xl: 40px;
--padding-7xl: 26px;
--padding-12xs: 1px;
--padding-lgi: 19px;
--padding-20xl: 39px;
--padding-10xl: 29px;
--padding-7xs: 6px;
--padding-mid: 17px;
--padding-9xl: 28px;
--padding-10xs-5: 2.5px;
--padding-10xs: 3px;
--padding-102xl: 121px;
--padding-base: 16px;
--padding-76xl-9: 95.9px;
--padding-17xl: 36px;
--padding-14xl: 33px;
--padding-16xl: 35px;
--padding-8xs-5: 4.5px;
--padding-xs: 12px;
--padding-56xl: 75px;

/* Border Radiuses */
--br-3xs: 10px;
--br-7xs: 6px;
--br-9xs: 4px;
--br-xl: 20px;
--br-8xs-9: 4.9px;
  
  }
